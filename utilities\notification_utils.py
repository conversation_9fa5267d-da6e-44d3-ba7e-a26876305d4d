from datetime import datetime
from typing import Dict, Optional
import uuid
import firebase_admin
from firebase_admin import credentials, messaging

import config as cfg

# --- Firebase Initialization ---
cred = credentials.Certificate(cfg.FIREBASE_SECURE_JSON)
firebase_admin.initialize_app(cred)


# --- FCM Client using Firebase Admin SDK ---
class FCMClient:
    def send(self, token: str, title: str, body: str, data: Dict[str, str]) -> bool:
        message = messaging.Message(
            token=token,
            notification=messaging.Notification(title=title, body=body),
            data={k: str(v) for k, v in data.items()}  # FCM data must be strings
        )
        try:
            messaging.send(message)
            print(f"[FCM] Sent to {token}: {title} - {body}")
            return True
        except Exception as e:
            print(f"[FCM ERROR] Failed to send to {token}: {e}")
            return False


# --- Templates for Notifications ---
class NotificationTemplates:
    @staticmethod
    def chat(data):
        return {
            "title": f"New message from {data['sender']}",
            "body": f"{data['message'][:50]}...",
            "data": {"timestamp": data['timestamp'].isoformat()}
        }

    @staticmethod
    def consultation_created(role, data):
        if role == "patient":
            return {
                "title": "Consultation Scheduled",
                "body": f"Your consultation with Dr. {data['doctor']} is scheduled for {data['date']} at {data['time']}.",
                "data": {}
            }
        elif role == "doctor":
            return {
                "title": "New Consultation Scheduled",
                "body": f"You have a new consultation with {data['patient']} on {data['date']} at {data['time']}.",
                "data": {}
            }
        elif role == "receptionist":
            return {
                "title": "Consultation Scheduled",
                "body": f"Consultation for {data['patient']} with Dr. {data['doctor']} on {data['date']} at {data['time']}.",
                "data": {}
            }

    @staticmethod
    def consultation_reminder(role, data):
        if role == "patient":
            return {
                "title": "Upcoming Consultation",
                "body": f"Reminder: Your consultation with Dr. {data['doctor']} starts in 30 minutes.",
                "data": {}
            }
        elif role == "doctor":
            return {
                "title": "Upcoming Consultation",
                "body": f"Reminder: Your consultation with {data['patient']} starts in 30 minutes.",
                "data": {}
            }
        elif role == "receptionist":
            return {
                "title": "Upcoming Consultation",
                "body": f"Reminder: Consultation for {data['patient']} with Dr. {data['doctor']} starts in 30 minutes.",
                "data": {}
            }

    @staticmethod
    def transcription_complete(data):
        return {
            "title": "Transcription Ready",
            "body": f"Transcription for your consultation with {data['patient']} has been completed. Click to review.",
            "data": {}
        }

    @staticmethod
    def final_remarks(data):
        return {
            "title": "Doctor's Remarks Available",
            "body": f"Dr. {data['doctor']} has submitted final remarks for your consultation. Click to view.",
            "data": {}
        }


# --- Notification Utility Class ---
class NotificationService:
    def __init__(self):
        self.fcm = FCMClient()
        self.retry_log = []

    def notify_user(self, event: str, role: str, recipient_token: str, data: Dict):
        template_func = getattr(NotificationTemplates, event, None)
        if not template_func:
            raise ValueError(f"Unknown notification event: {event}")

        try:
            if event in ["consultation_created", "consultation_reminder"]:
                template = template_func(role, data)
            else:
                template = template_func(data)
            success = self.fcm.send(recipient_token, template['title'], template['body'], template.get("data", {}))
            if not success:
                self.retry_log.append({
                    "id": str(uuid.uuid4()),
                    "token": recipient_token,
                    "template": template,
                    "timestamp": datetime.utcnow().isoformat()
                })
        except Exception as e:
            print(f"[ERROR] Notification failed: {e}")

    def retry_failed(self):
        for entry in self.retry_log:
            print(f"Retrying notification {entry['id']}")
            self.fcm.send(entry["token"], entry["template"]["title"], entry["template"]["body"],
                          entry["template"].get("data", {}))
        self.retry_log.clear()

# --- Singleton instance to use across the project ---
# notifier = NotificationService()
