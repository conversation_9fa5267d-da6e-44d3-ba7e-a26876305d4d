from cryptography.fernet import <PERSON>rnet
import base64
import os
import json
import uuid

# Use a Fernet key from environment or generate a new one (for demo)
FERNET_KEY = os.environ.get("ORG_PACKAGE_FERNET_KEY")
if not FERNET_KEY:
    FERNET_KEY = Fernet.generate_key()
fernet = Fernet(FERNET_KEY)


def generate_package_request_id():
    return str(uuid.uuid4())


def generate_encrypted_payment_link(base_url, package_info):
    """
    package_info: dict with org/package/payment info
    base_url: e.g. https://yourdomain.com/pay
    Returns: full payment link with encrypted payload
    """
    payload = json.dumps(package_info).encode()
    token = fernet.encrypt(payload).decode()
    return f"{base_url}?token={token}"


def decrypt_payment_token(token):
    try:
        payload = fernet.decrypt(token.encode())
        return json.loads(payload)
    except Exception:
        return None
