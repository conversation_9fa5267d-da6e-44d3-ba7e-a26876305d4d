import os
from dotenv import load_dotenv

# from utilities.app_utils import get_git_branch

load_dotenv()

# current_branch = get_git_branch()

# if current_branch == "dev":
#     MONGO_URI = os.getenv("DEV_MONGO_URI")
# elif current_branch == "stage":
#     MONGO_URI = os.getenv("STAGE_MONGO_URI")
# else:
#     MONGO_URI = os.getenv("LOCAL_MONGO_URI")
MONGO_URI = os.getenv("DEV_MONGO_URI")

SECRET_KEY = os.getenv("SECRET_KEY")
SMTP_SERVER = os.getenv("SMTP_SERVER")
SMTP_PORT = int(os.getenv("SMTP_PORT"))
SMTP_USERNAME = os.getenv("SMTP_USERNAME")
SMTP_PASSWORD = os.getenv("SMTP_PASSWORD")
UPLOAD_AUDIO_FOLDER = 'audio_recordings'
ALLOWED_AUDIO_EXTENSIONS = {'wav', 'mp3', 'm4a', 'webm'}
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
SUMMARY_FIELDS = {
    "summary_overview",
    "patient_overview",
    "reason_for_visit",
    "history_of_present_illness",
    "past_medical_history",
    "medications",
    "vitals",
    "risk_factors",
    "diagnostic_plan",
    "treatment_plan",
    "follow_up"
}
PRESCRIPTION_FIELDS = {
    "medications",
    "diagnosis",
    "notes",
    "medical_history",
    "doctor_signature"
}

ZOOM_API_KEY = os.getenv('ZOOM_API_KEY')
ZOOM_API_SECRET = os.getenv('ZOOM_API_SECRET')
ZOOM_USER_ID = os.getenv('ZOOM_USER_ID')
# ZOOM_CREATE_MEETING_URL = f"https://api.zoom.us/v2/users/{ZOOM_USER_ID}/meetings"
ZOOM_CREATE_MEETING_URL = f"https://api.zoom.us/v2/users/me/meetings"
ZOOM_TOKEN = "eyJzdiI6IjAwMDAwMiIsImFsZyI6IkhTNTEyIiwidiI6IjIuMCIsImtpZCI6Ijc3NmY1ODdiLWRjYjgtNDUyZC05NjY3LTQyNWFkZWY2ZTJjZSJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cPMpgQrdgAFhvxW3pYHMavyf2cqu5ObEMXoitZ92dS2SG2-SNLVSJDfZr4I8iAIdN4X2zJ79pw866xMIFP7UCw"
ZOOM_REFRESH_TOKEN = "eyJzdiI6IjAwMDAwMiIsImFsZyI6IkhTNTEyIiwidiI6IjIuMCIsImtpZCI6IjhmYmZlYzJjLWJmYWYtNGNkZS05OGVlLTRiMDFkZGQ1MGQxYiJ9.eyJhdWQiOiJodHRwczovL29hdXRoLnpvb20udXMiLCJ1aWQiOiJEMkRPaDE5U1FQNlpJcEtiNV9yVEZnIiwidmVyIjoxMCwiYXVpZCI6IjMxNzgxY2VkOGFlNjQ0ZmNjNzBkOTg2NTk4MjJmYWRkMTM0ZjJiYWE2NzI2NDYyMjBkZWU1YmU2YzI3ZGYxOWYiLCJuYmYiOjE3NTA0MTU0MzMsImNvZGUiOiJJZ0k1WHVrakFMTnUtVXRfS3pxUmZXbjA0aVRyOGJ0VnciLCJpc3MiOiJ6bTpjaWQ6bTJyUjQ4cDhUWkd1aXFyRkdaX3R3IiwiZ25vIjowLCJleHAiOjE3NTgxOTE0MzMsInR5cGUiOjEsImlhdCI6MTc1MDQxNTQzMywiYWlkIjoiQ3BoaFRVRW5UakNpVmFCbUhNOUhyZyJ9.K5qIlTGjmchKw9wvbOMuMqjPhGtGYKNQUBY5aRbCNerpo6B50kFPiab7bDRr0LluRfxen30ouIbuCBh9caoKvQ"
ZOOM_CLIENT_ID = "m2rR48p8TZGuiqrFGZ_tw"
ZOOM_CLIENT_SECRET = "1k5dPIXLTI4L4nLjuegPXHiCLKOgcCYs"
ZOOM_TOKEN_EXPIRY = 3599
ZOOM_TOKEN_URL = "https://zoom.us/oauth/token"
ZOOM_WEBHOOK_SECRET = ""
KYC_API_URL = os.getenv('KYC_API_URL', "https://api.smileidentity.com/v1")
KYC_PARTNER_ID = os.getenv('KYC_PARTNER_ID', "7420")
KYC_API_KEY = os.getenv('KYC_API_KEY', "d6c06761-3960-4b29-8449-4e79dea37b50")
KYC_AUTH_TOKEN = os.getenv('KYC_AUTH_TOKEN',
                           "c52fdi2Dw4BHWMoY5QRRcVAXsTkAp0Tf3QTz89Cklyf4Q5/uEmXDq+aTc87EAJDrjdX29W1kuXcQUFtUIvJtocDTQIHMivK+bqSUK/kLDus8kuc5KWGNvDsOkDSoxRe6WhrIWALGClcmMM//DLXs4rcqZWcwbNjTbp8hqEAf4xI=")
UPLOAD_CERTIFICATES_FOLDER = "uploads/certificates"
ALLOWED_CERTIFICATES_EXTENSIONS = {'pdf', 'jpeg', 'jpg', 'png'}
FIREBASE_SECURE_JSON = os.getenv('FIREBASE_SECURE_JSON', 'evernote-md-firebase-adminsdk-fbsvc-a4bffed76b.json')

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY = os.getenv('STRIPE_PUBLISHABLE_KEY', "pk_test_51MnhxCH94hWUHrW24MGyx9vm3zG5Q1rKi1goZveMg0PxqV7QjLZ0E2Wilp6I72C3fEhwOfH9Y5AjVMuzRepKZU7C006te99g6T")
STRIPE_SECRET_KEY = os.getenv('STRIPE_SECRET_KEY', "sk_test_51MnhxCH94hWUHrW2axuoJp5cT6o6P15vGCaSLEE77QSWvBmgPVO1bgw0yZL6w818mj8vashsTjsVpsx1hSleX0po00cbqeEUGz")
STRIPE_WEBHOOK_SECRET = os.getenv('STRIPE_WEBHOOK_SECRET', "whsec_tYWn23I86VA37TgWnQSz8v7Mtk5lIVf9")
status_messages = {
            "Suspended": "Your account has been suspended. Please contact admin.",
            "Pending": "Your account is pending approval. Please wait for admin approval.",
            "Hold": "Your account is on hold. Please contact admin.",
            "Rejected": "Your account has been rejected. Please contact admin.",
            "Approved": "Your account is approved but not active. Please contact admin."
        }
