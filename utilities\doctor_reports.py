import csv
import io
from datetime import datetime
from bson import ObjectId

def aggregate_doctor_performance(mongo, org_id=None, start_date=None, end_date=None):
    """
    Aggregates doctor performance metrics: total consultations, average feedback score.
    Optionally filter by organization and date range.
    Returns a list of dicts: [{doctor_id, name, email, org_id, total_consultations, avg_feedback}]
    """
    users_query = {"user_type": "doctor"}
    if org_id:
        users_query["organization_id"] = org_id
    doctors = list(mongo.db.users.find(users_query, {"_id": 1, "name": 1, "email": 1, "organization_id": 1}))
    results = []
    for doc in doctors:
        doctor_id = str(doc["_id"])
        consult_query = {"doctor_id": doctor_id}
        if start_date and end_date:
            consult_query["date"] = {"$gte": start_date, "$lte": end_date}
        total_consultations = mongo.db.consultations.count_documents(consult_query)
        # Feedback: assume feedback is stored in consultations as 'feedback_score' (1-5)
        feedback_scores = [c.get("feedback_score") for c in mongo.db.consultations.find(consult_query, {"feedback_score": 1}) if c.get("feedback_score") is not None]
        avg_feedback = round(sum(feedback_scores) / len(feedback_scores), 2) if feedback_scores else None
        results.append({
            "doctor_id": doctor_id,
            "name": doc.get("name", ""),
            "email": doc.get("email", ""),
            "organization_id": doc.get("organization_id", ""),
            "total_consultations": total_consultations,
            "avg_feedback": avg_feedback
        })
    return results

def generate_doctor_report_csv(doctor_metrics):
    """
    doctor_metrics: list of dicts from aggregate_doctor_performance
    Returns: (filename, file-like object)
    """
    output = io.StringIO()
    writer = csv.writer(output)
    writer.writerow(["Doctor Name", "Email", "Organization ID", "Total Consultations", "Average Feedback Score"])
    for doc in doctor_metrics:
        writer.writerow([
            doc.get("name", ""),
            doc.get("email", ""),
            doc.get("organization_id", ""),
            doc.get("total_consultations", 0),
            doc.get("avg_feedback") if doc.get("avg_feedback") is not None else "N/A"
        ])
    output.seek(0)
    filename = f"doctor_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    return filename, output 