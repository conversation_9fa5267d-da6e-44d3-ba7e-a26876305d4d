# import subprocess
from uuid import uuid4
from datetime import datetime
from config import ALLOWED_AUDIO_EXTENSIONS
from mutagen.mp3 import MP3
from io import BytesIO


# def get_git_branch():
#     try:
#         branch = subprocess.check_output(["git", "rev-parse", "--abbrev-ref", "HEAD"]).strip().decode("utf-8")
#         return branch
#     except Exception:
#         return None  # Return None if not in a Git repo


def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_AUDIO_EXTENSIONS


def allowed_cert_file(filename):
    allowed_extensions = {'pdf', 'jpeg', 'jpg', 'png'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in allowed_extensions


def generate_unique_filename(consultation_id):
    date_str = datetime.now().strftime('%d%m%Y')
    unique_seq = str(uuid4().hex[:8])
    return f"{consultation_id}-{date_str}-{unique_seq}"


def get_audio_duration(audio_stream):
    # Ensure it's a BytesIO object
    if isinstance(audio_stream, bytes):
        audio_stream = BytesIO(audio_stream)

    # Use mutagen to load the audio from the BytesIO stream
    audio = MP3(audio_stream)

    # Get the duration of the audio in seconds
    duration_seconds = audio.info.length

    # Check if the duration is less than 7 seconds
    if duration_seconds < 7:
        return False
    return True
