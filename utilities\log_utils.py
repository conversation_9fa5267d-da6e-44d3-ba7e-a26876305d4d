import datetime
import traceback

def log_event(
    mongo_db,
    event_type,
    api_endpoint,
    status,
    error_message=None,
    user_id=None,
    role=None,
    request_data=None,
    stack_trace=None
):
    """
    Log an event to the api_logs collection.

    Event Type Table:
    | Event Type             | Log on Success? | Log on Failure? | Example event_type         |
    |------------------------|-----------------|-----------------|---------------------------|
    | API Error              | —               | Yes             | api_error                 |
    | Auth Error             | —               | Yes             | auth_error                |
    | External Service Call  | Yes             | Yes             | external_service_error     |
    | Notification Sent      | Yes             | Yes             | notification_sent          |
    | Security Event         | Yes             | Yes             | security_event             |
    """
    log_entry = {
        "timestamp": datetime.datetime.utcnow(),
        "event_type": event_type,
        "api_endpoint": api_endpoint,
        "status": status,
        "user_id": user_id,
        "role": role,
        "error_message": error_message,
        "request_data": request_data,
        "stack_trace": stack_trace,
    }
    # Remove None values for cleanliness
    log_entry = {k: v for k, v in log_entry.items() if v is not None}
    try:
        mongo_db.db.api_logs.insert_one(log_entry)
    except Exception as e:
        print(f"Failed to log event: {e}") 