# @chat_bp.route('/messages/<receiver_id>', methods=['GET'])
# @jwt_required()
# def get_chat(receiver_id):
#     db = current_app.db
#     current_user = get_jwt_identity()
#     messages = list(db.messages.find({
#         "$or": [
#             {"sender_id": current_user, "receiver_id": receiver_id},
#             # {"sender_id": receiver_id, "receiver_id": current_user}
#         ]
#     }).sort("timestamp", 1))
#     return jsonify(messages)
#
#
# # routes/attachment.py
# from flask import Blueprint, request, jsonify
# from flask_jwt_extended import jwt_required
# from werkzeug.utils import secure_filename
# import os
#
# attachment_bp = Blueprint('attachment', __name__)
# UPLOAD_FOLDER = 'uploads'
# os.makedirs(UPLOAD_FOLDER, exist_ok=True)
#
#
# @attachment_bp.route('/upload', methods=['POST'])
# @jwt_required()
# def upload_file():
#     file = request.files['file']
#     allowed_types = ['application/pdf', 'image/jpeg', 'image/png']
#     if file.mimetype not in allowed_types:
#         return jsonify(msg="Unsupported file type. Please upload PDFs or images."), 400
#
#     file.seek(0, os.SEEK_END)
#     size = file.tell()
#     file.seek(0)
#
#     if size > 10 * 1024 * 1024:
#         return jsonify(msg="File too large. Maximum size allowed is 10 MB."), 400
#
#     filename = secure_filename(file.filename)
#     file_path = os.path.join(UPLOAD_FOLDER, filename)
#     file.save(file_path)
#     return jsonify(url=f"/{file_path}", type=file.mimetype.split('/')[-1])
#
#
# # socket/chat_socket.py
# from flask_socketio import emit, join_room
# from datetime import datetime
#
#
# def init_socket(socketio, db):
#     @socketio.on('join')
#     def on_join(data):
#         join_room(data['room'])  # Typically user ID
#
#     @socketio.on('send_message')
#     def handle_message(data):
#         msg = {
#             "sender_id": data['sender_id'],
#             "receiver_id": data['receiver_id'],
#             "text": data['text'],
#             "timestamp": datetime.utcnow(),
#             "attachment": data.get("attachment"),
#             "patient_context": data.get("patient_context")
#         }
#         db.messages.insert_one(msg)
#         emit("receive_message", msg, room=data['receiver_id'])
#         emit("receive_message", msg, room=data['sender_id'])
#
#     @socketio.on('typing')
#     def handle_typing(data):
#         emit('typing', {'user_id': data['user_id']}, room=data['receiver_id'])
#
#     @socketio.on('notification')
#     def notify_user(data):
#         emit('notification', {
#             "sender_name": data["sender_name"],
#             "message": data["message"],
#             "patient_name": data.get("patient_name", "")
#         }, room=data["receiver_id"])