# Use Python 3.9.10 as the base image
FROM python:3.9.10-slim

# Set the working directory inside the container
WORKDIR /app

# Copy the requirements file into the container
COPY requirements.txt .

# Install the dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of the application code
COPY . .

# Expose the port that the Flask app will run on
EXPOSE 8000


# Command to run the application
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "app:app"]
