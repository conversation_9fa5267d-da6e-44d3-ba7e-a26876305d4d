from openai import OpenAI
from config import OPENAI_API_KEY
import json
import os

client = OpenAI(api_key=OPENAI_API_KEY)

# def process_audio_transcription(file_path: str):
#     try:
#         with open(file_path, "rb") as audio_file:
#             response = client.audio.transcriptions.create(
#                 model="whisper-1",
#                 file=audio_file,
#                 response_format="verbose_json",
#                 timestamp_granularities=["segment"]
#             )
#
#         transcript = response.text
#         segments = [
#             {
#                 "segment_id": segment.id,
#                 "speaker": f"Speaker 0{segment.id % 2 + 1}",
#                 "start_time": segment.start,
#                 "end_time": segment.end,
#                 "transcribed_text": segment.text
#             }
#             for segment in getattr(response, "segments", [])
#         ]
#
#         return {
#             "status": True,
#             "message": "Transcription completed",
#             "transcription": transcript,
#             "segments": segments
#         }
#
#     except Exception as e:
#         return {"status": False, "message": "Failed to transcribe audio", "error": str(e)}

"""
def get_audio_duration(file_path: str) -> float:
    # Get audio duration in seconds for WAV files.
    try:
        with contextlib.closing(wave.open(file_path, 'r')) as f:
            frames = f.getnframes()
            rate = f.getframerate()
            return frames / float(rate)
    except Exception:
        return -1.0  # Unsupported format for this method


def process_audio_transcription(file_path: str):
    try:
        # Optional: check file size or duration
        duration = get_audio_duration(file_path)
        if duration > 600:  # 10 minutes
            return {
                "status": False,
                "message": "Audio too long. Please split into smaller chunks (<10 mins recommended)."
            }

        with open(file_path, "rb") as audio_file:
            response = client.audio.transcriptions.create(
                model="whisper-1",
                file=audio_file,
                response_format="verbose_json",
                timestamp_granularities=["segment"]
            )

        transcript = response.text.strip()
        segments = [
            {
                "segment_id": segment.id,
                "speaker": f"Speaker 0{segment.id % 2 + 1}",
                "start_time": segment.start,
                "end_time": segment.end,
                "transcribed_text": segment.text
            }
            for segment in getattr(response, "segments", [])
        ]

        if not transcript or len(transcript.split()) < 5:
            return {
                "status": False,
                "message": "Transcription appears incomplete or too short.",
                "transcription": transcript,
                "segments": segments
            }

        return {
            "status": True,
            "message": "Transcription completed",
            "transcription": transcript,
            "segments": segments
        }

    except Exception as e:
        return {"status": False, "message": "Failed to transcribe audio", "error": str(e)}
"""


def process_audio_transcription(audio_stream, filename):
    try:
        if audio_stream is None:
            return {
                "status": False,
                "message": "Invalid or empty audio stream",
                "error": "audio too short"
            }

        audio_stream.seek(0)
        response = client.audio.transcriptions.create(
            model="whisper-1",
            file=(filename, audio_stream),
            response_format="verbose_json",
            timestamp_granularities=["segment"],
            language="en"  # Force transcription in English
        )

        transcript = response.text
        segments = [
            {
                "segment_id": segment.id,
                "speaker": f"Speaker 0{segment.id % 2 + 1}",
                "start_time": segment.start,
                "end_time": segment.end,
                "transcribed_text": segment.text
            }
            for segment in getattr(response, "segments", [])
        ]

        if len(transcript.strip()) < 10 or len(segments) == 0:
            return {
                "status": False,
                "message": "Transcription likely incomplete",
                "error": "Very short or empty result received from API",
                "transcription": transcript,
                "segments": segments
            }

        return {
            "status": True,
            "message": "Transcription completed",
            "transcription": transcript,
            "segments": segments
        }

    except Exception as e:
        return {"status": False, "message": "Failed to transcribe audio", "error": str(e)}


def generate_transcription_summary(transcript: str, patient_name: str = "") -> dict:
    """
    Generates a structured patient summary from a transcript using OpenAI GPT model.
    Allows for slightly flexible summarization while avoiding name hallucination and unsupported assumptions.
    """
    prompt = f"""
You are a medical documentation assistant.

Given the following clinical conversation transcript between a doctor and a patient, generate a structured summary in JSON format that can be used in a patient dashboard.

### INSTRUCTIONS:
- Use the patient name **"{patient_name}"** exactly as provided. Do **not** make up or change the patient's name.
- Base your summary only on the information available in the transcript, but you may paraphrase or slightly infer meaning when context is reasonably clear.
- If a section is clearly not mentioned or not relevant, return the message of "no relevant information found".
- If the transcript is too short or unclear, you may summarize minimally, but avoid fabricating details.
- Keep the tone professional and medically accurate, but not overly rigid.
- The response in each field should be sentence. Do not return the object format.

### FORMAT (strict JSON only — no introductory or closing text):
{{
  "summary_overview": "",
  "patient_overview": "",
  "reason_for_visit": "",
  "history_of_present_illness": "",
  "past_medical_history": "",
  "medications": "",
  "vitals": "",
  "risk_factors": "",
  "diagnostic_plan": "",
  "treatment_plan": "",
  "follow_up": ""
}}

### TRANSCRIPT:
\"\"\"
{transcript}
\"\"\"
"""

    response = client.chat.completions.create(
        model="gpt-4",
        messages=[
            {"role": "system",
             "content": "You are a medical assistant helping to create structured patient summaries."},
            {"role": "user", "content": prompt}
        ],
        temperature=0.2
    )

    content = response.choices[0].message.content

    try:
        start = content.find('{')
        end = content.rfind('}') + 1
        json_str = content[start:end]
        return {"status": True, "summary": json.loads(json_str)}
    except Exception as e:
        return {"status": False, "raw_output": content, "error": str(e)}

# def generate_transcription_summary(transcript: str, patient_name: str = "") -> dict:
#     """
#     Generates a structured patient summary from a transcript using OpenAI GPT model.
#     Validates for content sufficiency and uses provided patient name to prevent hallucination.
#     """
#     prompt = f"""
# You are a highly accurate medical documentation assistant.
#
# Given the following clinical conversation transcript between a doctor and a patient, generate a structured summary in JSON format.
#
# ### INSTRUCTIONS:
# - Use the patient name **"{patient_name}"** exactly as provided. **Do not fabricate or assume a name**.
# - If any section of the summary is not addressed or the transcript lacks sufficient clinical content, return an empty string ("") — do not write messages like "Insufficient data provided."
# - Do **not** generalize or assume medical details that are not explicitly stated in the transcript.
# - If the transcript appears to be too short or lacks relevant information, generate empty fields with a message like "Insufficient data provided."
# - Be precise, avoid assumptions, and adhere to strict clinical documentation tone.
#
# ### FORMAT (strict JSON only, no extra text):
# {{
#   "summary_overview": "",
#   "patient_overview": "",
#   "reason_for_visit": "",
#   "history_of_present_illness": "",
#   "past_medical_history": "",
#   "medications": "",
#   "vitals": "",
#   "risk_factors": "",
#   "diagnostic_plan": "",
#   "treatment_plan": "",
#   "follow_up": ""
# }}
#
# ### TRANSCRIPT:
# \"\"\"
# {transcript}
# \"\"\"
# """
#
#     response = client.chat.completions.create(
#         model="gpt-4",
#         messages=[
#             {"role": "system",
#              "content": "You are a medical assistant helping to create structured patient summaries."},
#             {"role": "user", "content": prompt}
#         ],
#         temperature=0.1
#     )
#
#     content = response.choices[0].message.content
#
#     try:
#         start = content.find('{')
#         end = content.rfind('}') + 1
#         json_str = content[start:end]
#         return {"status": True, "summary": json.loads(json_str)}
#     except Exception as e:
#         return {"status": False, "raw_output": content, "error": str(e)}
