name: CI/CD Pipeline for Dev and Stage

on:
  push:
    branches:
      - dev   # Trigger pipeline for dev branch
      - stage # Trigger pipeline for stage branch

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Deploy to Dev Server
        if: github.ref == 'refs/heads/dev'
        uses: appleboy/ssh-action@v0.1.10
        with:
          host: ${{ secrets.DEV_EC2_IP }}
          username: ubuntu
          password: ${{ secrets.SSH_DEV_PASSWORD }}
          port: 22
          script: |
            echo "Deploying to Dev Server..."
            # Navigate to the project directory
            cd /home/<USER>/backend

            # Pull the latest code
            git fetch --all
            git reset --hard origin/dev

            # Build the new Docker image
            docker build -t evernote-dev .

            # Start the new container on a temporary port (8001)
            docker run -d --log-driver=awslogs --log-opt awslogs-region=me-central-1 \
            --log-opt awslogs-group=evernotemd-dev-backend --log-opt awslogs-create-group=true \
            -v /home/<USER>/media:/app/media \
            -p 8001:8000 --name evernote-dev-new evernote-dev

            # Health check for the new container
            echo "Waiting for the new container to start..."
            sleep 10  # Adjust sleep time as needed
            if docker ps --filter "name=evernote-dev-new" --format "{{.Status}}" | grep -q "Up"; then
              echo "New container is running. Stopping the old container..."

              # Stop and remove the old container
              docker stop evernote-dev || true
              docker rm evernote-dev || true

              # Stop the new container and restart it on port 8000
              docker stop evernote-dev-new || true
              docker rm evernote-dev-new || true
              docker run -d --log-driver=awslogs --log-opt awslogs-region=me-central-1 \
              --log-opt awslogs-group=evernotemd-dev-backend --log-opt awslogs-create-group=true \
              -v /home/<USER>/media:/app/media \
              -p 8000:8000 --name evernote-dev evernote-dev

              # Delete unused Docker images
              echo "Cleaning up unused Docker images..."
              docker image prune -f  # Delete dangling images
              docker rmi $(docker images -q evernote-dev) || true  # Delete old evernote-dev images

              echo "Dev deployment completed successfully with zero downtime!"
            else
              echo "New container failed to start. Rolling back..."
              docker stop evernote-dev-new || true
              docker rm evernote-dev-new || true
              docker rmi $(docker images -q evernote-dev) || true  # Delete old evernote-dev images
              exit 1
            fi

      - name: Deploy to Stage Server
        if: github.ref == 'refs/heads/stage'
        uses: appleboy/ssh-action@v0.1.10
        with:
          host: ${{ secrets.STAGE_EC2_IP }}
          username: ubuntu
          password: ${{ secrets.SSH_STAGE_PASSWORD }}
          port: 22
          script: |
            echo "Deploying to Stage Server..."
            echo "inside server"
            # Navigate to the project directory
            cd /home/<USER>/backend
            ls
            # Pull the latest code
            git fetch --all
            git reset --hard origin/stage

            # Build the new Docker image
            docker build -t evernote-stage-backend .
            # Start the new container on a temporary port (8001)
            docker run -d -it  --name evernote-stage-backend-new --log-driver=awslogs --log-opt awslogs-region=me-central-1 --log-opt awslogs-group=evernotemd-stage-backend --log-opt awslogs-create-group=true -p 8001:8000 evernote-stage-backend
            


            # Health check for the new container
            echo "Waiting for the new container to start..."
            sleep 10  # Adjust sleep time as needed
            if docker ps --filter "name=evernote-stage-backend-new" --format "{{.Status}}" | grep -q "Up"; then
              echo "New container is running. Stopping the old container..."

              # Stop and remove the old container
              docker stop evernote-stage-backend || true
              docker rm evernote-stage-backend || true

              # Stop the new container and restart it on port 8000
              docker stop evernote-stage-backend-new || true
              docker rm evernote-stage-backend-new || true
              docker run -d -it --name evernote-stage-backend --log-driver=awslogs --log-opt awslogs-region=me-central-1 --log-opt awslogs-group=evernotemd-stage-backend --log-opt awslogs-create-group=true -p 8000:8000 evernote-stage-backend

              # Delete unused Docker images
              echo "Cleaning up unused Docker images..."
              docker image prune -f  # Delete dangling images
              docker rmi $(docker images -q evernote-stage-backend) || true  # Delete old evernote-stage-backend images

              echo "Stage deployment completed successfully with zero downtime!"
            else
              echo "New container failed to start. Rolling back..."
              docker stop evernote-stage-backend-new || true
              docker rm evernote-stage-backend-new || true
              docker image prune -f  # Delete dangling images
              docker rmi $(docker images -q evernote-stage-backend) || true 
              exit 1
            fi
