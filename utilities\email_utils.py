from config import SMTP_SERVER, SMTP_PORT, SMTP_USERNAME, SMTP_PASSWORD
from cryptography.fernet import <PERSON>rnet
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.application import MIMEApplication
import datetime
import traceback
import os

AES_KEY = Fernet.generate_key()
cipher = Fernet(AES_KEY)


def send_email(to_email, subject, body):
    try:
        server = smtplib.SMTP(SMTP_SERVER, SMTP_PORT)
        server.starttls()
        server.login(SMTP_USERNAME, SMTP_PASSWORD)
        message = f"Subject: {subject}\n\n{body}"
        server.sendmail(SMTP_USERNAME, to_email, message)
        server.quit()
    except Exception as e:
        print(f"Error sending email: {e}")


def send_upload_notification_email_with_attachment(
        to_email, consultation_id, filename, transcription_segments, audio_filepath
):
    try:
        sender_email = SMTP_USERNAME
        sender_password = SMTP_PASSWORD
        smtp_server = SMTP_SERVER
        smtp_port = SMTP_PORT

        subject = f"New Audio Uploaded - Consultation ID: {consultation_id}"
        body = f"""
        A new audio file has been uploaded.

        Consultation ID: {consultation_id}
        Original Filename: {filename}
        Uploaded At: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
        Transcription Segment Count: {len(transcription_segments)}

        First Segment Preview:
        {transcription_segments[0] if transcription_segments else 'N/A'}
        """

        # Create email
        message = MIMEMultipart()
        message['From'] = sender_email
        message['To'] = to_email
        message['Subject'] = subject
        message.attach(MIMEText(body, 'plain'))

        # Attach audio file
        with open(audio_filepath, 'rb') as f:
            part = MIMEApplication(f.read(), Name=os.path.basename(audio_filepath))
            part['Content-Disposition'] = f'attachment; filename="{os.path.basename(audio_filepath)}"'
            message.attach(part)

        # Send email
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(sender_email, sender_password)
            server.send_message(message)

        return True
    except Exception as e:
        print(f"Failed to send email: {e}")
        return False


def send_error_notification_email(api_name, error_details, to_email):
    """
    Sends an error notification email with full traceback if an exception is passed.

    :param api_name: Name of the API where the error occurred
    :param error_details: Exception object or error message string
    :param to_email: Recipient email address
    :return: True if email sent successfully, False otherwise
    """
    try:
        sender_email = SMTP_USERNAME
        sender_password = SMTP_PASSWORD
        smtp_server = SMTP_SERVER
        smtp_port = SMTP_PORT

        subject = f"API Error Notification - {api_name}"

        # Format traceback if error_details is an exception
        if isinstance(error_details, BaseException):
            error_text = ''.join(
                traceback.format_exception(type(error_details), error_details, error_details.__traceback__))
        else:
            error_text = str(error_details)

        body = f"""
        An error occurred in the API: {api_name}

        Timestamp: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

        Error Details:
        {error_text}
        """

        # Create the email message
        message = MIMEMultipart()
        message['From'] = sender_email
        message['To'] = to_email
        message['Subject'] = subject
        message.attach(MIMEText(body, 'plain'))

        # Send email
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(sender_email, sender_password)
            server.send_message(message)

        return True
    except Exception as e:
        print(f"Failed to send error notification email: {e}")
        return False


def send_registration_otp_email(to_email, name, otp):
    subject = "Verify Your Email Address to Complete Registration"
    html = f"""
    <!DOCTYPE html>
    <html lang=\"en\">
      <head>
        <meta charset=\"UTF-8\" />
        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />
        <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />
        <title>Verify Your Email Address to Complete Registration</title>
        <style type=\"text/css\">
          body {{ margin: 0; padding: 0; background-color: #f9f9f9; font-family: 'Inter', Arial, sans-serif; }}
          @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap');
          table {{ border-spacing: 0; }}
          td {{ padding: 0; }}
          img {{ border: 0; }}
          .wrapper {{ width: 100%; table-layout: fixed; background-color: #f9f9f9; padding-bottom: 40px; padding-top: 40px; border-top: 10px solid #568399; }}
          .container {{ width: 100%; max-width: 600px; margin: 0 auto; background-color: #ffffff; }}
          .header {{ background-color: #ffffff; padding: 20px; text-align: left; color: #1c1c1c; }}
          .header-table {{ width: 100%; max-width: 600px; margin: 0 auto; padding-bottom: 20px; border-bottom: 1px solid #f9f9f9; }}
          .header img {{ max-width: 100px; vertical-align: middle; }}
          .header .title {{ text-align: right; font-size: 16px; font-weight: bold; color: #1c1c1c; vertical-align: middle; }}
          .verification-box {{ background-color: #ffffff; padding: 40px; text-align: center; border-radius: 8px; }}
          .verification-box h1 {{ font-size: 24px; margin-bottom: 10px; color: #1c1c1c; font-family: 'Inter', Arial, sans-serif; }}
          .verification-box p {{ font-size: 16px; margin-bottom: 20px; color: #666666; font-family: 'Inter', Arial, sans-serif; }}
          .verification-code {{ font-size: 36px; letter-spacing: 5px; font-weight: bold; margin-bottom: 30px; color: #1c1c1c; font-family: 'Inter', Arial, sans-serif; }}
          .copy-btn {{ background-color: #568399; color: #ffffff; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-size: 18px; font-family: 'Inter', Arial, sans-serif; display: inline-block; }}
          .footer {{ text-align: center; margin-top: 30px; font-size: 14px; color: #666666; font-family: 'Inter', Arial, sans-serif; padding-bottom: 20px; }}
          .footer a {{ color: #568399; text-decoration: none; font-family: 'Inter', Arial, sans-serif; }}
          .social-icons img {{ width: 24px; margin-right: 10px; }}
          @media only screen and (max-width: 600px) {{ .container {{ width: 100% !important; }} .header .title {{ text-align: center; margin-top: 10px; }} }}
        </style>
      </head>
      <body>
        <table class=\"wrapper\" role=\"presentation\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" >
          <tr><td><table class=\"container\" role=\"presentation\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" >
            <tr><td class=\"header\"><table class=\"header-table\" role=\"presentation\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" >
              <tr><td><img src=\"https://app-dev.evernotemd.com/assets/logo-4x-DZ9Boskw.png\" alt=\"Logo\" /></td>
              <td class=\"title\">Verify Your Email Address to Complete Registration</td></tr></table></td></tr>
            <tr><td class=\"verification-box\"><h1>Welcome, {name}!</h1><p>To complete your registration and verify your email address, please enter the One-Time Password (OTP) below:<br /><strong>Your OTP:</strong></p><div class=\"verification-code\">{otp}</div><p>This code is valid for 10 minutes and can only be used once.<br />If you didn’t sign up for an account, please ignore this email.<br /><strong>Welcome aboard!</strong></p></td></tr>
            <tr><td class=\"footer\"><h3>Need Help?</h3><p>Need Help? Please send any feedback or bug reports<br />to <a href=\"mailto:<EMAIL>\"><EMAIL></a></p></td></tr>
          </table></td></tr>
        </table>
      </body>
    </html>
    """
    _send_html_email(to_email, subject, html)


def send_otp_email(to_email, name, otp, context_word="login"):
    # context_word: e.g. 'login', 'reset password', 'email verification', etc.
    subject = f"One-Time Password (OTP) for {context_word.title()}"
    context_word_cap = context_word.title()
    html = f"""
    <!DOCTYPE html>
    <html lang=\"en\">
      <head>
        <meta charset=\"UTF-8\" />
        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />
        <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />
        <title>One-Time Password (OTP) for {context_word_cap}</title>
        <style type=\"text/css\">
          body {{ margin: 0; padding: 0; background-color: #f9f9f9; font-family: 'Inter', Arial, sans-serif; }}
          @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap');
          table {{ border-spacing: 0; }}
          td {{ padding: 0; }}
          img {{ border: 0; }}
          .wrapper {{ width: 100%; table-layout: fixed; background-color: #f9f9f9; padding-bottom: 40px; padding-top: 40px; border-top: 10px solid #568399; }}
          .container {{ width: 100%; max-width: 600px; margin: 0 auto; background-color: #ffffff; }}
          .header {{ background-color: #ffffff; padding: 20px; text-align: left; color: #1c1c1c; }}
          .header-table {{ width: 100%; max-width: 600px; margin: 0 auto; padding-bottom: 20px; border-bottom: 1px solid #f9f9f9; }}
          .header img {{ max-width: 100px; vertical-align: middle; }}
          .header .title {{ text-align: right; font-size: 16px; font-weight: bold; color: #1c1c1c; vertical-align: middle; }}
          .verification-box {{ background-color: #ffffff; padding: 40px; text-align: center; border-radius: 8px; }}
          .verification-box h1 {{ font-size: 24px; margin-bottom: 10px; color: #1c1c1c; font-family: 'Inter', Arial, sans-serif; }}
          .verification-box p {{ font-size: 16px; margin-bottom: 20px; color: #666666; font-family: 'Inter', Arial, sans-serif; }}
          .verification-code {{ font-size: 36px; letter-spacing: 5px; font-weight: bold; margin-bottom: 30px; color: #1c1c1c; font-family: 'Inter', Arial, sans-serif; }}
          .copy-btn {{ background-color: #568399; color: #ffffff; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-size: 18px; font-family: 'Inter', Arial, sans-serif; display: inline-block; }}
          .footer {{ text-align: center; margin-top: 30px; font-size: 14px; color: #666666; font-family: 'Inter', Arial, sans-serif; padding-bottom: 20px; }}
          .footer a {{ color: #568399; text-decoration: none; font-family: 'Inter', Arial, sans-serif; }}
          .social-icons img {{ width: 24px; margin-right: 10px; }}
          @media only screen and (max-width: 600px) {{ .container {{ width: 100% !important; }} .header .title {{ text-align: center; margin-top: 10px; }} }}
        </style>
      </head>
      <body>
        <table class=\"wrapper\" role=\"presentation\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" >
          <tr><td><table class=\"container\" role=\"presentation\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" >
            <tr><td class=\"header\"><table class=\"header-table\" role=\"presentation\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" >
              <tr><td><img src=\"https://app-dev.evernotemd.com/assets/logo-4x-DZ9Boskw.png\" alt=\"Logo\" /></td>
              <td class=\"title\">One-Time Password (OTP) for {context_word_cap}</td></tr></table></td></tr>
            <tr><td class=\"verification-box\"><h1>Welcome back, {name}!</h1><p>We received a request to {context_word.lower()} to your account. Please use the One-Time Password (OTP) below to complete your {context_word.lower()}:<br /><strong>Your OTP</strong></p><div class=\"verification-code\">{otp}</div><p>This code is valid for the next 10 minutes and can only be used once. If you did not initiate this {context_word.lower()} request, please ignore this email or contact our support team immediately.</p></td></tr>
            <tr><td class=\"footer\"><h3>Need Help?</h3><p>Need Help? Please send any feedback or bug reports<br />to <a href=\"mailto:<EMAIL>\"><EMAIL></a></p></td></tr>
          </table></td></tr>
        </table>
      </body>
    </html>
    """
    _send_html_email(to_email, subject, html)


def _send_html_email(to_email, subject, html):
    try:
        sender_email = SMTP_USERNAME
        sender_password = SMTP_PASSWORD
        smtp_server = SMTP_SERVER
        smtp_port = SMTP_PORT
        message = MIMEMultipart()
        message['From'] = sender_email
        message['To'] = to_email
        message['Subject'] = subject
        message.attach(MIMEText(html, 'html'))
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(sender_email, sender_password)
            server.send_message(message)
        return True
    except Exception as e:
        print(f"Failed to send HTML email: {e}")
        return False
