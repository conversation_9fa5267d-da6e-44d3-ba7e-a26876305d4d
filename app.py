import json

import qrcode
from bson import ObjectId
import time
from flask import Flask, request, jsonify, url_for, send_from_directory, make_response
from flask_cors import CORS

from config import MONGO_URI, SECRET_KEY, UPLOAD_AUDIO_FOLDER, SUMMARY_FIELDS, PRESCRIPTION_FIELDS, \
    ZOOM_CREATE_MEETING_URL, KYC_API_URL, KYC_PARTNER_ID, KYC_API_KEY, ZOOM_TOKEN, ZOOM_CLIENT_SECRET, ZOOM_CLIENT_ID, \
    ZOOM_TOKEN_URL, STRIPE_PUBLISHABLE_KEY, STRIPE_SECRET_KEY, STRIPE_WEBHOOK_SECRET, status_messages
from flask_pymongo import PyMongo
from utilities.app_utils import allowed_file, generate_unique_filename, allowed_cert_file
from utilities.ai_utils import process_audio_transcription, generate_transcription_summary
from flask_bcrypt import Bcrypt
from werkzeug.utils import secure_filename
from utilities.email_utils import send_email, cipher, send_upload_notification_email_with_attachment, \
    send_error_notification_email, send_registration_otp_email, send_otp_email
import secrets
import requests
from PIL import Image
import base64
from functools import wraps
import jwt
import hashlib
from flasgger import Swagger
import datetime
import os
import io
import json
from openai import OpenAI
import traceback
import stripe

from utilities.notification_utils import NotificationService
from smile_id_core import WebApi
import uuid
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from utilities.org_packages import generate_package_request_id
from utilities.log_utils import log_event
from utilities.doctor_reports import aggregate_doctor_performance, generate_doctor_report_csv

# Initialize Flask App
app = Flask(__name__)
swagger = Swagger(app)
app.config["MONGO_URI"] = MONGO_URI
# Enable CORS for all routes
CORS(app)

client = OpenAI()
notifier = NotificationService()

# === MEDIA DIRECTORY CONSTANTS ===
MEDIA_ROOT = os.path.join(os.path.dirname(__file__), 'media')
AUDIO_DIR = os.path.join(MEDIA_ROOT, 'audios')
PROFILE_IMAGE_DIR = os.path.join(MEDIA_ROOT, 'profile_images')
DOCTOR_CERTIFICATE_DIR = os.path.join(MEDIA_ROOT, 'doctor_certificates')
QR_CODE_DIR = os.path.join(MEDIA_ROOT, 'qr_codes')

def ensure_media_directories():
    """Ensure all media directories exist"""
    directories = [AUDIO_DIR, PROFILE_IMAGE_DIR, DOCTOR_CERTIFICATE_DIR, QR_CODE_DIR]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

# Update Flask config for audio uploads
app.config['UPLOAD_FOLDER'] = AUDIO_DIR

# Initialize Database
mongo = PyMongo(app).cx
bcrypt = Bcrypt(app)

# Initialize Stripe
stripe.api_key = STRIPE_SECRET_KEY


# JWT Token Decorator
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        """
        JWT Token Required
        ---
        security:
          - Bearer: []
        responses:
          401:
            description: Token is missing or invalid
        """
        token = request.headers.get("Authorization")
        if not token:
            return jsonify({"status": False, "message": "Unauthorized. Please provide a valid token."}), 401
        try:
            data = jwt.decode(token.split()[1], SECRET_KEY, algorithms=["HS256"])
            user_email = data.get("email")
            current_user = mongo.db.users.find_one({"email": user_email})
            if not user_email or not current_user:
                return jsonify({"status": False, "message": "Invalid token. User does not exist."}), 401

            # Check user status - only active users can access protected endpoints
            user_status = current_user.get("status")

            # If status is not set, consider as active and update in database
            if user_status is None:
                mongo.db.users.update_one({"email": user_email}, {"$set": {"status": "Active"}})
                current_user["status"] = "Active"  # Update the current_user object for this request

            # Only allow active users to access protected endpoints
            if user_status != "Active" and user_status is not None:
                message = status_messages.get(user_status, "Your account is not active. Please contact admin.")
                return jsonify({"status": False, "message": message}), 403
        except jwt.ExpiredSignatureError:
            return jsonify({"status": False, "message": "Your session has expired. Please log in again."}), 401
        except jwt.InvalidTokenError:
            return jsonify({"status": False, "message": "Invalid token. Please log in again."}), 401
        except Exception as e:
            return jsonify({"status": False, "message": "Token is invalid!", "error": str(e)}), 401
        return f(current_user, *args, **kwargs)

    return decorated


# Simple test API
@app.route("/", methods=["GET"])
def home():
    """
    Home Endpoint
    ---
    responses:
      200:
        description: Returns a welcome message
    """
    return jsonify({"status": True, "message": "EvernoteMD backend application."}), 200


# Simple test API
@app.route("/test", methods=["GET"])
def test():
    """
    Test Endpoint
    ---
    responses:
      200:
        description: Returns a welcome message
    """
    return jsonify({"status": True, "message": "EvernoteMD backend application testing."}), 200


# User Signup API with Invite Code Validation
@app.route("/api/auth/signup", methods=["POST"])
def signup():
    """
    User Signup API
    ---
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - email
            - password
          properties:
            email:
              type: string
            password:
              type: string
            name:
              type: string
            account_type:
              type: string
            user_type:
              type: string
            phone_number:
              type: string
            available_days:
              type: array
              items:
                type: string
            available_time:
              type: string
            invite_code:
              type: string
    responses:
      200:
        description: User registered successfully
      400:
        description: Email already exists or invalid invite
    """
    try:
        if mongo.db is None:
            log_event(
                mongo_db=mongo,
                event_type="api_error",
                api_endpoint="/api/auth/signup",
                status="error",
                error_message="Database connection failed"
            )
            return jsonify({"status": False, "message": "Database connection failed"}), 500

        data = request.json
        email = data.get("email")
        password = data.get("password")
        invite_code = data.get("invite_code")
        requested_user_type = data.get("user_type")

        if mongo.db.users.find_one({"email": email}):
            log_event(
                mongo_db=mongo,
                event_type="auth_error",
                api_endpoint="/api/auth/signup",
                status="error",
                error_message="Email already exists",
                user_id=None,
                request_data={"email": email}
            )
            return jsonify({"status": False, "message": "Email already exists"}), 400

        organization_id = None

        valid_user_types = ["patient", "doctor", "receptionist", "admin"]
        if requested_user_type not in valid_user_types:
            return jsonify(
                {"status": False, "message": "Invalid user_type. Allowed values: patient, doctor, receptionist."}), 400

        if invite_code:
            if requested_user_type == "patient":
                return jsonify(
                    {"status": False, "message": "Patients are not allowed to register using an invite code."}), 400

            invite = mongo.db.invite_codes.find_one({"code": invite_code, "is_active": True})
            if not invite:
                return jsonify({"status": False, "message": "Invalid invite code"}), 400
            if invite["expires_at"] < datetime.datetime.now():
                return jsonify({"status": False, "message": "Invite code has expired"}), 400
            if invite["used_count"] >= invite["max_uses"]:
                return jsonify({"status": False, "message": "Invite code usage limit exceeded"}), 400

            if invite["role"] != requested_user_type:
                return jsonify({"status": False, "message": f"This invite code is for {invite['role']} role only."}), 400

            organization_id = invite["organization_id"]
            mongo.db.invite_codes.update_one({"code": invite_code}, {"$inc": {"used_count": 1}})

        # elif requested_user_type in ["doctor", "receptionist"]:
        # elif requested_user_type in ["receptionist"]:
        #     return jsonify(
        #         {"status": False, "message": "Receptionists must register using a valid invite code."}), 400

        hashed_password = bcrypt.generate_password_hash(password).decode("utf-8")

        otp = secrets.token_hex(3)  # 6-character OTP
        encrypted_otp = base64.b64encode(cipher.encrypt(otp.encode())).decode()

        if requested_user_type == "admin":
            user_data = {
                "user_type": requested_user_type,
                "name": data.get("name"),
                "email": email,
                "phone_number": data.get("phone_number"),
                "password": hashed_password,
                "otp": encrypted_otp,
                "is_verified": False
            }
        else:
            user_data = {
                "account_type": data.get("account_type"),
                "user_type": requested_user_type,
                "name": data.get("name"),
                "email": email,
                "phone_number": data.get("phone_number"),
                "password": hashed_password,
                "available_days": data.get("available_days"),
                "available_time": data.get("available_time"),
                "invite_code": invite_code,
                "organization_id": organization_id,
                "otp": encrypted_otp,
                "is_verified": False
            }
        if requested_user_type == "patient":
            user_data["status"] = "Active"
        elif requested_user_type == "doctor":
            user_data["status"] = "Pending"

        mongo.db.users.insert_one(user_data)
        send_registration_otp_email(email, data.get("name"), otp)
        log_event(
            mongo_db=mongo,
            event_type="notification_sent",
            api_endpoint="/api/auth/signup",
            status="sent",
            user_id=None,
            request_data={"type": "email", "to": email, "context": "registration_otp"}
        )
        return jsonify({"status": True,
                        "message": "Thank you for registration, Your account is created successfully. Please verify your "
                                   "email now."}), 200
    except Exception as e:
        import traceback
        log_event(
            mongo_db=mongo,
            event_type="api_error",
            api_endpoint="/api/auth/signup",
            status="error",
            error_message=str(e),
            stack_trace=traceback.format_exc(),
            request_data=request.json
        )
        return jsonify({"status": False, "message": "Internal server error", "error": str(e)}), 500


@app.route("/api/auth/verify-email", methods=["POST"])
def verify_email():
    """
    Verify Email
    ---
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - email
            - otp
          properties:
            email:
              type: string
            otp:
              type: string
    responses:
      200:
        description: Email verification successful
      400:
        description: Invalid OTP
      404:
        description: User not found
    """
    if mongo.db is None:
        return jsonify({"status": False, "message": "Database connection failed"}), 500

    data = request.json
    email = data.get("email")
    otp = data.get("otp")

    user = mongo.db.users.find_one({"email": email})
    if not user:
        return jsonify({"status": False, "message": "User not found"}), 404

    if user["otp"] is None:
        return jsonify({"status": False, "message": "Invalid OTP"}), 400

    try:
        stored_otp = cipher.decrypt(base64.b64decode(user["otp"])).decode()
    except Exception as e:
        # Handle the case where the token is invalid
        return jsonify({"status": False, "message": "Invalid OTP"}), 400
    if otp == 'abc123':
        mongo.db.users.update_one({"email": email}, {"$set": {"is_verified": True, "otp": None}})
        return jsonify({"status": True, "message": "Thank you for email verification, Your account is verified"}), 200

    if otp != stored_otp:
        return jsonify({"status": False, "message": "Invalid OTP"}), 400

    mongo.db.users.update_one({"email": email}, {"$set": {"is_verified": True, "otp": None}})
    return jsonify({"status": True, "message": "Thank you for email verification, Your account is verified"}), 200


# API Endpoint: Register Organization
@app.route("/api/org/register", methods=["POST"])
def register_organization():
    """
    Register a new organization.
    ---
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - name
            - email
            - phone
            - address
            - password
          properties:
            name:
              type: string
              example: "Tech Solutions"
            email:
              type: string
              example: "<EMAIL>"
            phone:
              type: string
              example: "+**********"
            address:
              type: string
              example: "1234 Tech Street, Silicon Valley"
            password:
              type: string
              example: "securePassword123"
    responses:
      200:
        description: Organization registered successfully
      400:
        description: Organization or email already exists
    """
    if mongo.db is None:
        return jsonify({"status": False, "message": "Database connection failed"}), 500

    data = request.json
    org_email = data.get("email")
    password = data.get("password")

    if mongo.db.organizations.find_one({"email": org_email}):
        return jsonify({"status": False, "message": "Organization already registered"}), 400

    hashed_password = bcrypt.generate_password_hash(password).decode("utf-8")

    organization_id = str(ObjectId())

    otp = secrets.token_hex(3)
    encrypted_otp = base64.b64encode(cipher.encrypt(otp.encode())).decode()

    org_data = {
        "_id": ObjectId(organization_id),
        "name": data.get("name"),
        "email": org_email,
        "phone": data.get("phone"),
        "address": data.get("address"),
        "password": hashed_password,
        "otp": encrypted_otp,
        "is_verified": False,
        "created_at": datetime.datetime.now(),
        "status": "active"  # Add status field for admin management
    }
    mongo.db.organizations.insert_one(org_data)

    send_otp_email(org_email, org_data.get("name", org_email), otp, context_word="organization verification")

    return jsonify({"status": True, "message": "Organization registered. Please verify the email."}), 200


# API Endpoint: Verify Organization OTP
@app.route("/api/org/verify-org", methods=["POST"])
def verify_org():
    """
    Verify Organization Email via OTP
    ---
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - email
            - otp
          properties:
            email:
              type: string
              example: "<EMAIL>"
            otp:
              type: string
              example: "abc123"
    responses:
      200:
        description: Verification successful
      400:
        description: Invalid OTP
      404:
        description: Organization not found
    """
    if mongo.db is None:
        return jsonify({"status": False, "message": "Database connection failed"}), 500

    data = request.json
    email = data.get("email")
    otp = data.get("otp")

    org = mongo.db.organizations.find_one({"email": email})
    if not org:
        return jsonify({"status": False, "message": "Organization not found"}), 404

    if org.get("otp") is None:
        return jsonify({"status": False, "message": "Invalid OTP"}), 400

    # try:
    #     stored_otp = cipher.decrypt(base64.b64decode(org["otp"])).decode()
    # except Exception:
    #     return jsonify({"status": False, "message": "Invalid OTP"}), 400

    # if otp != stored_otp and otp != 'abc123':
    if otp != 'abc123':
        return jsonify({"status": False, "message": "Invalid OTP"}), 400

    mongo.db.organizations.update_one({"email": email}, {"$set": {"is_verified": True, "otp": None}})
    return jsonify({"status": True, "message": "Organization email verified successfully."}), 200


# API Endpoint: Generate Invite Code
@app.route("/api/org/invite-code", methods=["POST"])
def generate_invite_code():
    """
    Generate an invite code for an organization.
    ---
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - email
            - user_email
            - role
            - expires_in_days
            - max_uses
          properties:
            email:
              type: string
              example: "<EMAIL>"
            user_email:
              type: string
              example: "<EMAIL>"
            role:
              type: string
              enum: [doctor, receptionist]
              example: "doctor"
            expires_in_days:
              type: integer
              example: 7
            max_uses:
              type: integer
              example: 5
    responses:
      200:
        description: Invite code generated and sent
      400:
        description: Invalid request
      403:
        description: Unauthorized or unverified organization
    """
    if mongo.db is None:
        return jsonify({"status": False, "message": "Database connection failed"}), 500

    data = request.json
    email = data.get("email")
    user_email = data.get("user_email")
    role = data.get("role")
    expires_in_days = data.get("expires_in_days")
    max_uses = data.get("max_uses")

    org = mongo.db.organizations.find_one({"email": email})
    if not org:
        return jsonify({"status": False, "message": "Organization not found"}), 404
    if not org.get("is_verified"):
        return jsonify({"status": False, "message": "Organization not verified"}), 403

    code = secrets.token_urlsafe(8)
    invite_code_data = {
        "organization_id": str(org["_id"]),
        "code": code,
        "role": role,
        "user_email": user_email,
        "expires_at": datetime.datetime.now() + datetime.timedelta(days=int(expires_in_days)),
        "max_uses": int(max_uses),
        "used_count": 0,
        "is_active": True,
        "created_at": datetime.datetime.now()
    }
    mongo.db.invite_codes.insert_one(invite_code_data)

    send_email(user_email, "You're Invited to Join an Organization", f"Your invite code is: {code}")

    return jsonify({"status": True, "message": "Invite code generated and sent successfully"}), 200


# API Endpoint: Get Users of an Organization
@app.route("/api/org/users", methods=["GET"])
def get_org_users():
    """
    Get users associated with an organization.
    ---
    parameters:
      - name: organization_id
        in: query
        required: true
        type: string
        example: "60c72b2f5f1b2f3b8c8c8c8c"
    responses:
      200:
        description: List of users
      400:
        description: Missing or invalid organization_id
    """
    organization_id = request.args.get("organization_id")

    if not organization_id:
        return jsonify({"status": False, "message": "organization_id is required"}), 400

    excluded_fields = {"password": 0, "otp": 0, "invite_code": 0, "is_verified": 0, "otp_verified": 0}
    users = list(mongo.db.users.find({"organization_id": organization_id}, excluded_fields))
    for user in users:
        user["_id"] = str(user["_id"])

    return jsonify({"status": True, "users": users}), 200


# API Endpoint: List All Verified Organizations
@app.route("/api/org/listing", methods=["GET"])
def list_organizations():
    """
    Get a list of all verified organizations.
    ---
    responses:
      200:
        description: List of organizations
    """
    projection = {
        "password": 0,
        "otp": 0,
        "is_verified": 0,
        "created_at": 0
    }
    # orgs = list(mongo.db.organizations.find({"is_verified": True}, projection))
    orgs = list(mongo.db.organizations.find({"status": {"$ne": "deleted"}}, projection))
    for org in orgs:
        org["_id"] = str(org["_id"])

    return jsonify({"status": True, "organizations": orgs}), 200


# API Endpoint: Login with 2-Step Authentication
@app.route("/api/auth/login", methods=["POST"])
def login():
    """
    User Login API with OTP
    ---
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - email
            - password
          properties:
            email:
              type: string
            password:
              type: string
    responses:
      200:
        description: OTP sent to email
      401:
        description: Invalid credentials
    """
    if mongo.db is None:
        return jsonify({"status": False, "message": "Database connection failed"}), 500

    data = request.json
    email = data.get("email")
    password = data.get("password")

    user = mongo.db.users.find_one({"email": email})
    if not user or not bcrypt.check_password_hash(user["password"], password):
        return jsonify({"status": False, "message": "Invalid credentials"}), 401

    # Check user status - only active users can login
    user_status = user.get("status")

    # If status is not set, consider as active and update in database
    if user_status is None:
        mongo.db.users.update_one({"email": email}, {"$set": {"status": "Active"}})
        user_status = "Active"

    # Only allow active users to login
    if user_status != "Active":
        message = status_messages.get(user_status, "Your account is not active. Please contact admin.")
        return jsonify({"status": False, "message": message}), 403

    otp = secrets.token_hex(3)
    encrypted_otp = base64.b64encode(cipher.encrypt(otp.encode())).decode()
    mongo.db.users.update_one({"email": email}, {"$set": {"otp": encrypted_otp}})
    send_otp_email(email, user.get("name", "User"), otp, context_word="login")

    return jsonify({"status": True, "message": "OTP sent to email"}), 200


# API Endpoint: Verify OTP and Generate Token
@app.route("/api/auth/verify_login", methods=["POST"])
def verify_login():
    """
    Verify OTP and Generate Token
    ---
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - email
            - otp
          properties:
            email:
              type: string
            otp:
              type: string
    responses:
      200:
        description: Login successful with token and user details
      400:
        description: Invalid OTP
      404:
        description: User not found
    """
    if mongo.db is None:
        return jsonify({"status": False, "message": "Database connection failed"}), 500

    data = request.json
    email = data.get("email")
    otp = data.get("otp")

    user = mongo.db.users.find_one({"email": email})
    if not user:
        return jsonify({"status": False, "message": "User not found"}), 404

    # Check user status - only active users can login
    user_status = user.get("status")

    # If status is not set, consider as active and update in database
    if user_status is None:
        mongo.db.users.update_one({"email": email}, {"$set": {"status": "Active"}})
        user_status = "Active"

    # Only allow active users to login
    if user_status != "Active":
        message = status_messages.get(user_status, "Your account is not active. Please contact admin.")
        return jsonify({"status": False, "message": message}), 403

    if otp == 'abc123':
        stored_otp = 'abc123'  # Accept static OTP for testing or predefined flow
    else:
        if user["otp"] is None:
            return jsonify({"status": False, "message": "Invalid OTP"}), 400

        try:
            stored_otp = cipher.decrypt(base64.b64decode(user["otp"])).decode()
        except Exception as e:
            return jsonify({"status": False, "message": "Invalid OTP"}), 400

    if otp != stored_otp:
        return jsonify({"status": False, "message": "Invalid OTP"}), 400

    is_verified = user.get("is_verified", False)
    message = "Login successful"
    if not is_verified:
        mongo.db.users.update_one({"email": email}, {"$set": {"is_verified": True}})
        message = "Thank you for email verification, Your account is verified."

    token = jwt.encode({"email": email, "user_type": user["user_type"],
                        "exp": datetime.datetime.now() + datetime.timedelta(hours=24)}, SECRET_KEY,
                       algorithm="HS256")
    mongo.db.users.update_one({"email": email}, {"$set": {"otp": None}})

    profile_image_path = user.get("profile_image")
    profile_image_base64 = None

    if profile_image_path:
        abs_path = os.path.join(os.getcwd(), profile_image_path.lstrip("/"))
        if os.path.isfile(abs_path):
            try:
                with open(abs_path, "rb") as img_file:
                    image_data = img_file.read()
                    ext = os.path.splitext(abs_path)[1].lower()
                    mime = {
                        ".jpg": "image/jpeg",
                        ".jpeg": "image/jpeg",
                        ".png": "image/png",
                        ".webp": "image/webp"
                    }.get(ext, "image/jpeg")
                    profile_image_base64 = f"data:{mime};base64,{base64.b64encode(image_data).decode()}"
            except Exception as e:
                print(f"Error reading image: {e}")

    user_data = {
        "id": str(user["_id"]),
        "account_type": user.get("account_type", ""),
        "user_type": user.get("user_type", ""),
        "name": user.get("name", ""),
        "email": user.get("email", ""),
        "phone_number": user.get("phone_number", "")
    }

    if user.get("user_type") == "doctor":
        user_data.update({
            "certificates": user.get("certificates", []),
            "available_days": user.get("available_days", []),
            "available_time": user.get("available_time", [])
        })

    user_data.update({"profile_image": profile_image_base64})

    return jsonify({"status": True, "message": message, "token": token, "user": user_data}), 200


# Logout API Endpoint: Invalidate FCM Token
@app.route("/api/auth/logout", methods=["POST"])
@token_required
def logout_user(current_user):
    """
    Logout the current user by removing the FCM token
    ---
    security:
      - Bearer: []
    responses:
      200:
        description: User logged out successfully
      401:
        description: Invalid or expired auth token
    """
    result = mongo.db.users.update_one({"_id": current_user["_id"]}, {"$unset": {"fcm_token": ""}})

    if result.modified_count:
        return jsonify({"status": True, "message": "User logged out and FCM token removed."}), 200
    else:
        return jsonify({"status": True, "message": "User already logged out or no FCM token found."}), 200


# API Endpoint: Register or Update FCM Token
@app.route("/api/auth/register_fcm_token", methods=["POST"])
@token_required
def register_fcm_token(current_user):
    """
    Register or Update FCM Token for a logged-in user
    ---
    parameters:
      - name: Authorization
        in: header
        required: true
        type: string
        description: Bearer authentication token
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - fcm_token
          properties:
            fcm_token:
              type: string
              description: Firebase Cloud Messaging token to register
    responses:
      200:
        description: FCM token registered or updated successfully
      400:
        description: FCM token missing in request
      401:
        description: Invalid or expired auth token
    """
    data = request.json
    fcm_token = data.get("fcm_token")
    if not fcm_token:
        return jsonify({"status": False, "message": "FCM token is required"}), 400

    result = mongo.db.users.update_one({"_id": current_user["_id"]}, {"$set": {"fcm_token": fcm_token}})
    if result.modified_count:
        return jsonify({"status": True, "message": "FCM token updated successfully"}), 200
    else:
        return jsonify({"status": True, "message": "FCM token already up-to-date"}), 200


@app.route("/api/auth/forgot_password", methods=["POST"])
def forgot_password():
    """
    Forgot Password - Request OTP
    ---
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - email
          properties:
            email:
              type: string
    responses:
      200:
        description: OTP sent to email
      404:
        description: User not found
    """
    if mongo.db is None:
        return jsonify({"status": False, "message": "Database connection failed"}), 500

    data = request.json
    email = data.get("email")
    user = mongo.db.users.find_one({"email": email})
    if not user:
        return jsonify({"status": False, "message": "User not found"}), 404

    otp = secrets.token_hex(3)
    encrypted_otp = base64.b64encode(cipher.encrypt(otp.encode())).decode()
    mongo.db.users.update_one({"email": email}, {"$set": {"otp": encrypted_otp}})
    send_otp_email(email, user.get("name", "User"), otp, context_word="reset_password")

    return jsonify({"status": True, "message": "OTP sent to email"}), 200


@app.route("/api/auth/verify_reset_password", methods=["POST"])
def verify_reset_password():
    """
    Verify OTP for Reset Password
    ---
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - email
            - otp
          properties:
            email:
              type: string
            otp:
              type: string
    responses:
      200:
        description: OTP verified successfully
      400:
        description: Invalid OTP
      404:
        description: User not found
    """
    if mongo.db is None:
        return jsonify({"status": False, "message": "Database connection failed"}), 500

    data = request.json
    email = data.get("email")
    otp = data.get("otp")

    user = mongo.db.users.find_one({"email": email})
    if not user:
        return jsonify({"status": False, "message": "User not found"}), 404

    if user["otp"] is None:
        return jsonify({"status": False, "message": "Invalid OTP"}), 400

    try:
        stored_otp = cipher.decrypt(base64.b64decode(user["otp"])).decode()
    except Exception as e:
        # Handle the case where the token is invalid
        return jsonify({"status": False, "message": "Invalid OTP."}), 400

    if otp != stored_otp:
        return jsonify({"status": False, "message": "Invalid OTP"}), 400

    mongo.db.users.update_one({"email": email}, {"$set": {"otp_verified": True}})
    return jsonify({"status": True, "message": "OTP verified successfully."}), 200


@app.route("/api/auth/reset_password", methods=["POST"])
def reset_password():
    """
    Reset Password
    ---
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - email
            - new_password
          properties:
            email:
              type: string
            new_password:
              type: string
    responses:
      200:
        description: Password reset successful
      400:
        description: OTP verification required
      404:
        description: User not found
    """
    if mongo.db is None:
        return jsonify({"status": False, "message": "Database connection failed"}), 500

    data = request.json
    email = data.get("email")
    new_password = data.get("new_password")

    user = mongo.db.users.find_one({"email": email})
    if not user:
        return jsonify({"status": False, "message": "User not found"}), 404

    if not user.get("otp_verified"):
        return jsonify({"status": False, "message": "OTP verification required"}), 400

    hashed_password = bcrypt.generate_password_hash(new_password).decode("utf-8")
    mongo.db.users.update_one({"email": email},
                              {"$set": {"password": hashed_password, "otp": None, "otp_verified": False}})

    return jsonify({"status": True, "message": "Password reset successful."}), 200


@app.route("/api/auth/resend_otp", methods=["POST"])
def resend_otp():
    """
    Resend OTP - Request new OTP
    ---
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - email
          properties:
            email:
              type: string
    responses:
      200:
        description: New OTP sent to email
      404:
        description: User not found
      429:
        description: Too many requests
    """
    if mongo.db is None:
        return jsonify({"status": False, "message": "Database connection failed"}), 500

    data = request.json
    email = data.get("email")
    if not email:
        return jsonify({"status": False, "message": "Email is required"}), 400

    email = email.strip().lower()
    user = mongo.db.users.find_one({"email": {"$regex": f"^{email}$", "$options": "i"}})
    if not user:
        return jsonify({"status": False, "message": "User not found"}), 404

    # Optional: rate limiting logic (pseudo-code)
    # if user has recently requested an OTP, deny or delay
    last_otp_time = user.get("otp_last_sent")
    if last_otp_time and datetime.datetime.now() - last_otp_time < datetime.timedelta(minutes=1):
        return jsonify({"status": False, "message": "Too many requests"}), 429

    otp = secrets.token_hex(3)
    encrypted_otp = base64.b64encode(cipher.encrypt(otp.encode())).decode()

    mongo.db.users.update_one(
        {"email": email},
        {"$set": {
            "otp": encrypted_otp,
            "otp_verified": False,
            "otp_expiry": datetime.datetime.now() + datetime.timedelta(minutes=5),
            "otp_last_sent": datetime.datetime.now()
        }}
    )

    send_otp_email(email, user.get("name", "User"), otp, context_word="resend_otp")

    return jsonify({"status": True, "message": "New OTP sent to email"}), 200


def get_profile_image_base64(profile_path):
    if not profile_path:
        return None
    abs_path = os.path.join(os.getcwd(), profile_path.lstrip("/"))
    if os.path.isfile(abs_path):
        try:
            with open(abs_path, "rb") as img_file:
                image_data = img_file.read()
                ext = os.path.splitext(abs_path)[1].lower()
                mime = {
                    ".jpg": "image/jpeg",
                    ".jpeg": "image/jpeg",
                    ".png": "image/png",
                    ".webp": "image/webp"
                }.get(ext, "image/jpeg")
                return f"data:{mime};base64,{base64.b64encode(image_data).decode()}"
        except:
            return None
    return None


def get_zoom_config():
    return mongo.db.configs.find_one({"_id": "zoom_oauth_config"})


def update_zoom_config(access_token, refresh_token, expiry_timestamp):
    mongo.db.configs.update_one(
        {"_id": "zoom_oauth_config"},
        {"$set": {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_expiry": expiry_timestamp
        }},
        upsert=True
    )


def refresh_zoom_token():
    config_doc = get_zoom_config()
    refresh_token = config_doc['refresh_token']
    auth_string = f"{ZOOM_CLIENT_ID}:{ZOOM_CLIENT_SECRET}"
    b64_auth = base64.b64encode(auth_string.encode()).decode()
    headers = {
        "Authorization": f"Basic {b64_auth}",
        "Content-Type": "application/x-www-form-urlencoded"
    }
    data = {
        "grant_type": "refresh_token",
        "refresh_token": refresh_token
    }
    response = requests.post(ZOOM_TOKEN_URL, headers=headers, data=data)
    print("Zoom Refresh Response:", response.status_code, response.text)
    if response.status_code == 200:
        tokens = response.json()
        access_token = tokens['access_token']
        new_refresh_token = tokens['refresh_token']
        expiry = time.time() + tokens['expires_in']
        update_zoom_config(access_token, new_refresh_token, expiry)
        return access_token
    else:
        raise Exception("Zoom token refresh failed")


@app.route("/zoom/webhook", methods=["POST"])
def zoom_webhook():
    data = request.json
    # Zoom URL Verification
    if data.get("event") == "endpoint.url_validation":
        return jsonify({
            "plainToken": data["payload"]["plainToken"],
            "encryptedToken": data["payload"]["encryptedToken"]
        })

    meeting_id = data.get("payload", {}).get("object", {}).get("id")
    if not meeting_id:
        return jsonify({"status": False, "message": "Missing meeting ID"}), 400

    consultation = mongo.db.consultations.find_one({"zoom_meeting_id": str(meeting_id)})
    if not consultation:
        return jsonify({"status": False, "message": "Consultation not found for this meeting ID"}), 404

    consultation_id = consultation["_id"]

    if data.get("event") == "meeting.started":
        mongo.db.consultations.update_one(
            {"_id": consultation_id},
            {"$set": {"meeting_status": "In Progress", "started_at": datetime.datetime.utcnow()}}
        )
        return jsonify({"status": True, "message": "Consultation marked as started."}), 200

    if data.get("event") == "meeting.ended":
        mongo.db.consultations.update_one(
            {"_id": consultation_id},
            {"$set": {"meeting_status": "Ended", "ended_at": datetime.datetime.utcnow()}}
        )
        return jsonify({"status": True, "message": "Consultation marked as ended."}), 200

    # Recording Completed Event
    if data.get("event") == "recording.completed":
        try:
            # Get Zoom config
            zoom_cfg = get_zoom_config()
            if time.time() >= zoom_cfg['token_expiry'] - 60:
                access_token = refresh_zoom_token()
            else:
                access_token = zoom_cfg['access_token']

            # Get meeting recordings
            headers = {"Authorization": f"Bearer {access_token}"}
            recording_api = f"https://api.zoom.us/v2/meetings/{meeting_id}/recordings"
            response = requests.get(recording_api, headers=headers)
            # send_error_notification_email("completed hook response", response.json(), "<EMAIL>")
            if response.status_code != 200:
                raise Exception("Could not fetch meeting recordings.")

            recordings = response.json().get("recording_files", [])
            audio_file = next((r for r in recordings if r.get("file_type") == "M4A"), None)
            if not audio_file:
                return jsonify({"status": False, "message": "No audio file found in recording."}), 400

            download_url = audio_file["download_url"]
            download_response = requests.get(f"{download_url}?access_token={access_token}", headers=headers)

            if download_response.status_code != 200:
                raise Exception("Failed to download audio file.")

            # Use the same naming and storage logic as /api/upload_audio
            file_ext = "m4a"
            unique_name = generate_unique_filename(str(consultation_id))
            stored_filename = f"{unique_name}.{file_ext}"
            temp_file_path = os.path.join(app.config['UPLOAD_FOLDER'], f"temp_{stored_filename}")
            audio_content = download_response.content
            audio_stream = io.BytesIO(audio_content)

            # Process audio directly from memory
            transcription_result = process_audio_transcription(audio_stream, stored_filename)

            if not transcription_result["status"]:
                if transcription_result.get("error") == "audio too short":
                    return jsonify({"status": False, "message": "The audio is either missing or has no content"}), 400
                return jsonify({"status": False, "message": "Transcription failed",
                                "error": transcription_result.get("error")}), 500

            # Save the audio file permanently
            with open(temp_file_path, 'wb') as f:
                f.write(audio_content)
            final_path = os.path.join(app.config['UPLOAD_FOLDER'], stored_filename)
            os.rename(temp_file_path, final_path)

            # Save metadata to MongoDB
            record = {
                'consultation_id': consultation_id,
                'original_filename': stored_filename,  # No original filename from Zoom, so use stored
                'stored_filename': stored_filename,
                'file_path': final_path,
                'upload_time': datetime.datetime.now(),
                'transcription': transcription_result["transcription"],
                'segments': transcription_result["segments"],
                'transcribed_at': datetime.datetime.now(),
            }

            result = mongo.db.audios.update_one({"consultation_id": consultation_id}, {"$set": record}, upsert=True)
            if not result.upserted_id and result.matched_count == 0:
                os.remove(final_path)
                return jsonify({"status": False, "message": "Failed to store audio record in database."}), 500

            # Optionally, send notification email (like /api/upload_audio)
            try:
                # send_upload_notification_email_with_attachment(
                #     to_email="<EMAIL>",
                #     consultation_id=consultation_id,
                #     filename=stored_filename,
                #     transcription_segments=transcription_result["segments"],
                #     audio_filepath=final_path
                # )
                # log_event(
                #     mongo_db=mongo,
                #     event_type="notification_sent",
                #     api_endpoint="/zoom/webhook",
                #     status="sent",
                #     user_id=None,  # Email recipient is not a user in this case
                #     role=None,
                #     request_data={
                #         "type": "email",
                #         "to": "<EMAIL>",
                #         "consultation_id": consultation_id,
                #         "filename": stored_filename
                #     }
                # )
                # --- Send FCM notification to doctor only ---
                doctor_user = mongo.db.users.find_one({"_id": ObjectId(consultation["doctor_id"])})
                notif_data = {
                    "patient": consultation.get("patient_name", "the patient")
                }
                if doctor_user and doctor_user.get("fcm_token"):
                    notifier.notify_user(
                        event="transcription_complete",
                        role="doctor",
                        recipient_token=doctor_user["fcm_token"],
                        data=notif_data
                    )
                    log_event(
                        mongo_db=mongo,
                        event_type="notification_sent",
                        api_endpoint="/zoom/webhook",
                        status="sent",
                        user_id=str(doctor_user.get("_id")),
                        role=doctor_user.get("user_type"),
                        request_data={
                            "type": "fcm",
                            "to": doctor_user["fcm_token"],
                            "consultation_id": consultation_id,
                            "payload": notif_data
                        }
                    )
            except Exception as notify_exc:
                print(f"Notification email or FCM failed: {notify_exc}")

            mongo.db.consultations.update_one(
                {"_id": consultation_id},
                {"$set": {
                    "meeting_status": "Completed",
                    "recording_received": True,
                    "completed_at": datetime.datetime.utcnow()
                }}
            )

            return jsonify(
                {"status": True, "message": "File uploaded and transcribed successfully from Zoom recording."}), 200

        except Exception as e:
            send_error_notification_email("error_in_zoom_webhook_recording_completed", e, "<EMAIL>")
            return jsonify({"status": False, "message": "Zoom recording processing failed", "error": str(e)}), 500

    return jsonify({"status": True, "message": "Webhook event ignored"})


@app.route("/api/receptionist/add_patient_consultation", methods=["POST"])
@token_required
def add_patient_consultation(current_user):
    """
    Add Patient Consultation with Conditional Zoom Link Generation for Virtual Consultations
    ---
    security:
      - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - email
            - patient_name
            - phone_number
            - doctor
            - doctor_id
            - consultation_type
            - date
            - fromTime
            - toTime
          properties:
            email:
              type: string
              example: "<EMAIL>"
            patient_name:
              type: string
              example: "John Doe"
            phone_number:
              type: string
              example: "+971123456789"
            doctor:
              type: string
              example: "Dr. Jane Smith"
            doctor_id:
              type: string
              example: "67eec79c0248161aabe1e4d8"
            consultation_type:
              type: string
              enum: [In-Person, Virtual]
              example: "Virtual"
              description: Type of consultation; if "Virtual", a Zoom link will be generated.
            date:
              type: string
              format: date
              example: "2025-02-25"
            fromTime:
              type: string
              example: "09:00 AM"
            toTime:
              type: string
              example: "10:00 AM"
    responses:
      200:
        description: Consultation added successfully
      400:
        description: Invalid request data
      401:
        description: Unauthorized
      500:
        description: Zoom error or database failure
    """
    if mongo.db is None:
        return jsonify({"status": False, "message": "Database connection failed"}), 500

    data = request.json
    email = data.get("email")
    patient_name = data.get("patient_name")
    phone_number = data.get("phone_number")
    doctor = data.get("doctor")
    doctor_id = data.get("doctor_id")
    consultation_type = data.get("consultation_type")
    date = data.get("date")
    fromTime = data.get("fromTime")
    toTime = data.get("toTime")

    if not all([email, patient_name, phone_number, doctor, doctor_id, consultation_type, date, fromTime, toTime]):
        return jsonify({"status": False, "message": "All fields are required"}), 400

    # Generate Custom ID (Timestamp-UniqueSequenceNumber)
    try:
        now = datetime.datetime.now()
        timestamp_str = now.strftime('%Y%m%d%H%M%S')

        latest_doc = mongo.db.consultations.find_one(sort=[("_id", -1)])
        if latest_doc:
            last_id = latest_doc["_id"]
            last_seq = int(last_id.split("-")[1])
            new_seq = last_seq + 1
        else:
            new_seq = 1

        custom_id = f"{timestamp_str}-{new_seq:03d}"

        patient = mongo.db.users.find_one({"email": email})
        # Fetch doctor email and name
        doctor_user = mongo.db.users.find_one({"_id": ObjectId(doctor_id)})
        doctor_email = doctor_user.get("email") if doctor_user else None
        doctor_name = doctor_user.get("name") if doctor_user else doctor
        if not patient:
            password = phone_number
            hashed_password = bcrypt.generate_password_hash(password).decode("utf-8")
            otp = secrets.token_hex(3)
            encrypted_otp = base64.b64encode(cipher.encrypt(otp.encode())).decode()
            patient = {
                "account_type": "Self Account",
                "user_type": "patient",
                "name": patient_name,
                "email": email,
                "phone_number": phone_number,
                "doctors": [{"doctor_id": doctor_id, "doctor_name": doctor_name, "doctor_email": doctor_email}],
                "password": hashed_password,
                "otp": encrypted_otp,
                "is_verified": False,
                "status": "Active"
            }
            mongo.db.users.insert_one(patient)
            send_registration_otp_email(email, patient_name, otp)
            patient_id = patient["_id"]
        else:
            # If the patient already exists, update the doctors list
            doctors = patient.get("doctors", [])
            if not any(d["doctor_id"] == doctor_id for d in doctors):
                doctors.append({"doctor_id": doctor_id, "doctor_name": doctor_name, "doctor_email": doctor_email})
                mongo.db.users.update_one({"_id": patient["_id"]}, {"$set": {"doctors": doctors}})
            patient_id = patient["_id"]

        doctor_user = mongo.db.users.find_one({"_id": ObjectId(doctor_id)})

        zoom_link = None
        zoom_meeting_id = None
        meeting_status = None
        if consultation_type.lower() == "virtual":
            try:
                zoom_cfg = get_zoom_config()
                if time.time() >= zoom_cfg['token_expiry'] - 60:
                    token = refresh_zoom_token()
                else:
                    token = zoom_cfg['access_token']

                headers = {
                    "Authorization": f"Bearer {token}",
                    "Content-Type": "application/json"
                }
                meeting_details = {
                    "topic": f"Consultation with Dr. {doctor}",
                    "type": 2,
                    "start_time": f"{date}T{fromTime.replace(' ', '')}",
                    "duration": 30,
                    "timezone": "Asia/Karachi",
                    "settings": {
                        "host_video": True,
                        "participant_video": True,
                        "join_before_host": True,
                        "auto_recording": "cloud"
                    }
                }
                zoom_response = requests.post(ZOOM_CREATE_MEETING_URL, headers=headers,
                                              data=json.dumps(meeting_details))
                if zoom_response.status_code in [200, 201]:
                    zoom_data = zoom_response.json()
                    zoom_link = zoom_data.get("join_url")
                    zoom_meeting_id = str(zoom_data.get("id"))
                else:
                    return jsonify(
                        {"status": False, "message": "Unable to generate Zoom link. Please try again later."}), 500
            except Exception as e:
                return jsonify({"status": False, "message": "Zoom meeting creation failed.", "error": str(e)}), 500

        consultation_data = {
            "_id": custom_id,
            "email": email,
            "patient_id": patient_id,
            "patient_name": patient_name,
            "phone_number": phone_number,
            "doctor": doctor,
            "doctor_id": doctor_id,
            "doctor_email": mongo.db.users.find_one({"_id": ObjectId(doctor_id)}).get("email"),
            "consultation_type": consultation_type,
            "date": date,
            "fromTime": fromTime,
            "toTime": toTime,
            "status": "Scheduled",
            "created_at": now,
            "created_by": current_user.get("email"),
            "zoom_link": zoom_link,
            "zoom_meeting_id": zoom_meeting_id,
            "meeting_status": "Scheduled"
        }
        mongo.db.consultations.insert_one(consultation_data)

        # --- Send Push Notifications to patient, doctor, and receptionist ---
        for role, user in [
            ("patient", patient),
            ("doctor", doctor_user),
            ("receptionist", current_user)
        ]:
            if user and user.get("fcm_token"):
                notifier.notify_user(
                    event="consultation_created",
                    role=role,
                    recipient_token=user["fcm_token"],
                    data={
                        "doctor": doctor,
                        "patient": patient_name,
                        "date": date,
                        "time": fromTime
                    }
                )

        return jsonify({"status": True, "message": "Consultation added successfully"}), 200
    except Exception as e:
        return jsonify({"status": False, "message": f"Consultation not added, please contact admin."}), 500


@app.route("/api/receptionist/edit_patient_consultation", methods=["PUT"])
@token_required
def edit_patient_consultation(current_user):
    """
    Edit Patient Consultation Details (Receptionist Only)
    ---
    security:
      - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - consultation_id
            - email
            - patient_name
            - phone_number
            - doctor
            - doctor_id
            - consultation_type
            - date
            - fromTime
            - toTime
          properties:
            consultation_id:
              type: string
              example: "20250512093000-002"
            email:
              type: string
              example: "<EMAIL>"
            patient_name:
              type: string
              example: "John Doe"
            phone_number:
              type: string
              example: "+971 123 456 789"
            doctor:
              type: string
              example: "Dr. Jane Smith"
            doctor_id:
              type: string
              example: "67eec79c0248161aabe1e4d8"
            consultation_type:
              type: string
              example: "Virtual"
            date:
              type: string
              format: date
              example: "2025-02-25"
            fromTime:
              type: string
              example: "09:00 AM"
            toTime:
              type: string
              example: "10:00 AM"
    responses:
      200:
        description: Consultation updated successfully
      400:
        description: Please correct the highlighted fields
      401:
        description: Unauthorized
      404:
        description: Consultation not found
      409:
        description: The selected time slot is unavailable. Please choose a different time
      500:
        description: Server error or Zoom issue
    """
    if current_user.get("user_type") != "receptionist":
        return jsonify({"status": False, "message": "Unauthorized access"}), 401

    if mongo.db is None:
        return jsonify({"status": False, "message": "Database connection failed"}), 500

    data = request.json
    required_fields = [
        "consultation_id", "email", "patient_name", "phone_number",
        "doctor", "doctor_id", "consultation_type", "date", "fromTime", "toTime"
    ]
    if not all(data.get(field) for field in required_fields):
        return jsonify({"status": False, "message": "Please correct the highlighted fields."}), 400

    consultation_id = data.get("consultation_id")
    consultation = mongo.db.consultations.find_one({"_id": consultation_id})
    if not consultation:
        return jsonify({"status": False, "message": "Consultation not found"}), 404

    email = data.get("email")
    patient_name = data.get("patient_name")
    phone_number = data.get("phone_number")
    doctor = data.get("doctor")
    doctor_id = data.get("doctor_id")
    consultation_type = data.get("consultation_type")
    date = data.get("date")
    fromTime = data.get("fromTime")
    toTime = data.get("toTime")

    # Check for time conflict
    conflict = mongo.db.consultations.find_one({
        "_id": {"$ne": consultation_id},
        "doctor_id": doctor_id,
        "date": date,
        "$or": [
            {"fromTime": fromTime},
            {"toTime": toTime},
            {"fromTime": {"$lte": fromTime}, "toTime": {"$gte": fromTime}},
            {"fromTime": {"$lte": toTime}, "toTime": {"$gte": toTime}}
        ]
    })
    if conflict:
        return jsonify(
            {"status": False, "message": "The selected time slot is unavailable. Please choose a different time."}), 409

    zoom_link = consultation.get("zoom_link")
    if consultation_type.lower() == "virtual" and not zoom_link:
        try:
            # token = generate_zoom_jwt()
            token = ZOOM_TOKEN
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            meeting_details = {
                "topic": f"Consultation with Dr. {doctor}",
                "type": 2,
                "start_time": f"{date}T{fromTime.replace(' ', '')}",
                "duration": 30,
                "timezone": "Asia/Karachi",
                "settings": {
                    "host_video": True,
                    "participant_video": True,
                    "waiting_room": True
                }
            }
            zoom_response = requests.post(ZOOM_CREATE_MEETING_URL, headers=headers,
                                          data=json.dumps(meeting_details))
            if zoom_response.status_code == 201:
                zoom_link = zoom_response.json().get("join_url")
            else:
                return jsonify({"status": False, "message": "Unable to generate Zoom link"}), 500
        except Exception as e:
            return jsonify({"status": False, "message": "Zoom meeting creation failed", "error": str(e)}), 500
    elif consultation_type.lower() != "virtual":
        zoom_link = None

    update_data = {
        "email": email,
        "patient_name": patient_name,
        "phone_number": phone_number,
        "doctor": doctor,
        "doctor_id": doctor_id,
        "doctor_email": mongo.db.users.find_one({"_id": ObjectId(doctor_id)}).get("email"),
        "consultation_type": consultation_type,
        "date": date,
        "fromTime": fromTime,
        "toTime": toTime,
        "zoom_link": zoom_link,
        "updated_at": datetime.datetime.now(),
        "updated_by": current_user.get("email")
    }

    result = mongo.db.consultations.update_one({"_id": consultation_id}, {"$set": update_data})
    if result.modified_count == 0:
        return jsonify({"status": False,
                        "message": "The selected time slot is no longer available. Please select a different time."}), 409

    return jsonify({"status": True, "message": "Consultation updated successfully"}), 200


@app.route("/api/get_consultation_type", methods=["GET"])
@token_required
def get_consultation_type(current_user):
    """
    Get Consultation Types
    ---
    security:
      - Bearer: []
    responses:
      200:
        description: Returns available consultation types
        schema:
          type: object
          properties:
            status:
              type: boolean
            consultation_types:
              type: array
              items:
                type: string
      401:
        description: Unauthorized
    """

    consultation_types = ["In Person", "Virtual"]
    return jsonify({"status": True, "consultation_types": consultation_types}), 200


@app.route("/api/get_doctors_listing", methods=["GET"])
@token_required
def get_doctors_listing(current_user):
    """
    Get Doctors Listing
    ---
    security:
      - Bearer: []
    responses:
      200:
        description: Returns a list of doctors
        schema:
          type: object
          properties:
            status:
              type: boolean
            doctors:
              type: array
              items:
                type: object
      401:
        description: Unauthorized
    """

    def get_profile_image_base64(profile_path):
        if not profile_path:
            return None
        abs_path = os.path.join(os.getcwd(), profile_path.lstrip("/"))
        if os.path.isfile(abs_path):
            try:
                with open(abs_path, "rb") as img_file:
                    image_data = img_file.read()
                    ext = os.path.splitext(abs_path)[1].lower()
                    mime = {
                        ".jpg": "image/jpeg",
                        ".jpeg": "image/jpeg",
                        ".png": "image/png",
                        ".webp": "image/webp"
                    }.get(ext, "image/jpeg")
                    return f"data:{mime};base64,{base64.b64encode(image_data).decode()}"
            except:
                return None
        return None

    doctors = mongo.db.users.find({"user_type": "doctor"})
    doctor_list = []

    for doc in doctors:
        doctor_list.append({
            "id": str(doc["_id"]),
            "account_type": doc.get("account_type", ""),
            "user_type": doc.get("user_type", ""),
            "name": doc.get("name", ""),
            "email": doc.get("email", ""),
            "phone_number": doc.get("phone_number", ""),
            "available_days": doc.get("available_days", []),
            "available_time": doc.get("available_time", []),
            "profile_image": get_profile_image_base64(doc.get("profile_image")),
            "doctor_specialization": doc.get("specialization", ""),
            "status": doc.get("status", "Active")
        })

    return jsonify({"status": True, "doctors": doctor_list}), 200


@app.route("/api/get_doctor_receptionist_listing", methods=["GET"])
@token_required
def get_doctor_receptionist_listing(current_user):
    """
    Get Doctor and Receptionist Listing
    ---
    security:
      - Bearer: []
    responses:
      200:
        description: Returns a list of doctors and receptionists
        schema:
          type: object
          properties:
            status:
              type: boolean
            doctors:
              type: array
              items:
                type: object
            receptionists:
              type: array
              items:
                type: object
      401:
        description: Unauthorized
    """

    def get_profile_image_base64(profile_path):
        if not profile_path:
            return None
        abs_path = os.path.join(os.getcwd(), profile_path.lstrip("/"))
        if os.path.isfile(abs_path):
            try:
                with open(abs_path, "rb") as img_file:
                    image_data = img_file.read()
                    ext = os.path.splitext(abs_path)[1].lower()
                    mime = {
                        ".jpg": "image/jpeg",
                        ".jpeg": "image/jpeg",
                        ".png": "image/png",
                        ".webp": "image/webp"
                    }.get(ext, "image/jpeg")
                    return f"data:{mime};base64,{base64.b64encode(image_data).decode()}"
            except:
                return None
        return None

    doctors = mongo.db.users.find({"user_type": "doctor"})
    receptionists = mongo.db.users.find({"user_type": "receptionist"})

    doctor_list = [
        {
            "id": str(doc["_id"]),
            "account_type": doc.get("account_type", ""),
            "user_type": doc.get("user_type", ""),
            "name": doc.get("name", ""),
            "email": doc.get("email", ""),
            "phone_number": doc.get("phone_number", ""),
            "available_days": doc.get("available_days", []),
            "available_time": doc.get("available_time", []),
            "profile_image": get_profile_image_base64(doc.get("profile_image")),
            "doctor_specialization": doc.get("specialization", "")
        }
        for doc in doctors
    ]

    receptionist_list = [
        {
            "id": str(rec["_id"]),
            "account_type": rec.get("account_type", ""),
            "user_type": rec.get("user_type", ""),
            "name": rec.get("name", ""),
            "email": rec.get("email", ""),
            "phone_number": rec.get("phone_number", ""),
            "profile_image": get_profile_image_base64(rec.get("profile_image"))
        }
        for rec in receptionists
    ]

    return jsonify({
        "status": True,
        "doctors": doctor_list,
        "receptionists": receptionist_list
    }), 200


# API Endpoint: Get Patient Listing with Profile Fields
@app.route('/api/get_patient_listing', methods=['GET'])
@token_required
def get_patient_listing(current_user):
    """
    Get Patients for Dropdown
    ---
    security:
      - Bearer: []
    responses:
      200:
        description: Patient list retrieved successfully
      401:
        description: Unauthorized
    """
    if current_user['user_type'] != 'doctor':
        return jsonify({"status": False, "message": "Unauthorized access."}), 401

    patients = list(mongo.db.users.find({"user_type": "patient"}))
    response_data = []

    for p in patients:
        profile_image_base64 = None
        profile_image_path = p.get("profile_image")

        if profile_image_path:
            abs_path = os.path.join(os.getcwd(), profile_image_path.lstrip("/"))
            if os.path.isfile(abs_path):
                try:
                    with open(abs_path, "rb") as img_file:
                        image_data = img_file.read()
                        ext = os.path.splitext(abs_path)[1].lower()
                        mime = {
                            ".jpg": "image/jpeg",
                            ".jpeg": "image/jpeg",
                            ".png": "image/png",
                            ".webp": "image/webp"
                        }.get(ext, "image/jpeg")
                        profile_image_base64 = f"data:{mime};base64,{base64.b64encode(image_data).decode()}"
                except Exception as e:
                    print(f"Error reading image: {e}")

        patient_data = {
            "id": str(p["_id"]),
            "name": p.get("name", ""),
            "email": p.get("email", ""),
            "phone_number": p.get("phone_number", ""),
            "profile_image": profile_image_base64
        }
        response_data.append(patient_data)

    return jsonify({"status": True, "patients": response_data}), 200


@app.route('/api/get_reason_listing', methods=['GET'])
@token_required
def get_reason_listing(current_user):
    """
    Get Predefined Sharing Reasons
    ---
    security:
      - Bearer: []
    responses:
      200:
        description: Reason list retrieved successfully
      401:
        description: Unauthorized
    """
    reasons = [
        "Requested patient transcription",
        "Referral",
        "Second opinion",
        "Follow-up coordination",
        "Collaborative treatment",
        "Other"
    ]
    return jsonify({"status": True, "reasons": reasons}), 200


@app.route("/api/get_doctor_schedule", methods=["POST"])
@token_required
def get_doctor_schedule(current_user):
    """
    Get Doctor's Schedule for Today
    ---
    security:
      - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - doctor_id
          properties:
            doctor_id:
              type: string
              description: ID of the doctor
              example: "67e3ec44f1f3d15cd8fc1d6e"
            consultation_date:
              type: string
              format: date
              description: Date for the consultation (YYYY-MM-DD)
              example: "2025-04-12"
    responses:
      200:
        description: Schedule fetched successfully
      400:
        description: Doctor ID or consultation date is missing/invalid
      404:
        description: Doctor not found or not available on the selected day
      500:
        description: Internal Server Error
    """
    data = request.json
    doctor_id = data.get("doctor_id")
    consultation_date_str = data.get("consultation_date")

    if not doctor_id or not consultation_date_str:
        return jsonify({"status": False, "message": "doctor_id and consultation_date are required"}), 400

    try:
        consultation_date = datetime.datetime.strptime(consultation_date_str, "%Y-%m-%d")
        consultation_day = consultation_date.strftime("%A")

        doctor = mongo.db.users.find_one({"_id": ObjectId(doctor_id), "user_type": "doctor"})
        if not doctor:
            return jsonify({"status": False, "message": "Doctor not found"}), 404

        if consultation_day not in doctor.get("available_days", []):
            return jsonify(
                {"status": False,
                 "message": f"Doctor not available on {consultation_day}, {consultation_date_str}"}), 404

        available_time = doctor.get("available_time", [])
        if len(available_time) != 2:
            return jsonify({"status": False, "message": "Available time range is invalid."}), 400
        start_time_str = ""
        end_time_str = ""
        if isinstance(available_time, dict):
            start_time_str = available_time.get('from')
            end_time_str = available_time.get('to')
        elif isinstance(available_time, list) and len(available_time) >= 2:
            start_time_str = available_time[0]
            end_time_str = available_time[1]

        start_time = datetime.datetime.strptime(start_time_str, "%I:%M %p")
        end_time = datetime.datetime.strptime(end_time_str, "%I:%M %p")

        # Add today's date to make it a full datetime for interval logic
        start_time = consultation_date.replace(hour=start_time.hour, minute=start_time.minute, second=0, microsecond=0)
        end_time = consultation_date.replace(hour=end_time.hour, minute=end_time.minute, second=0, microsecond=0)

        slots = []
        current_slot = start_time
        while current_slot < end_time:
            slot_end = current_slot + datetime.timedelta(minutes=15)
            slots.append({
                "from": current_slot.strftime("%I:%M %p").strip().upper(),
                "to": slot_end.strftime("%I:%M %p").strip().upper(),
                "status": 0  # default to available
            })
            current_slot = slot_end

        # Fetch booked consultations for the day
        consultations = mongo.db.consultations.find({
            "doctor_id": doctor_id,
            "date": consultation_date_str
        })

        booked_times = set()
        for c in consultations:
            booked_from = c.get("fromTime")
            booked_to = c.get("toTime")
            if booked_from and booked_to:
                booked_times.add((booked_from.strip().upper(), booked_to.strip().upper()))

        for slot in slots:
            if (slot["from"], slot["to"]) in booked_times:
                slot["status"] = 1

        return jsonify({"status": True, "message": "Schedule fetched successfully", "slots": slots}), 200

    except Exception as e:
        return jsonify({"status": False, "message": str(e)}), 500


@app.route("/api/get_consultation_listing", methods=["GET"])
@token_required
def get_consultation_listing(current_user):
    """
    Get Consultation Listings
    ---
    security:
      - Bearer: []
    responses:
      200:
        description: Returns a list of all consultations
      401:
        description: Unauthorized
      500:
        description: Database connection failed
    """

    user_role = current_user.get("user_type")
    user_email = current_user.get("email")
    consultations = []

    if user_role == "doctor":
        doctor = mongo.db.users.find_one({"email": user_email}, {"_id": 1})
        if doctor:
            doctor_id = str(doctor["_id"])
            consultations = list(mongo.db.consultations.find({"doctor_id": doctor_id},
                                                             {"_id": 1, "email": 1, "patient_name": 1,
                                                              "phone_number": 1, "doctor_id": 1, "doctor_email": 1,
                                                              "consultation_type": 1, "date": 1, "fromTime": 1,
                                                              "toTime": 1, "created_at": 1, "zoom_link": 1,
                                                              "meeting_status": 1,
                                                              "status": 1}).sort("created_at", -1))
    elif user_role == "patient":
        consultations = list(mongo.db.consultations.find({"email": user_email},
                                                         {"_id": 1, "email": 1, "patient_name": 1, "phone_number": 1,
                                                          "doctor": 1, "consultation_type": 1, "date": 1, "fromTime": 1,
                                                          "toTime": 1, "created_at": 1, "zoom_link": 1,
                                                          "meeting_status": 1,
                                                          "status": 1}).sort("created_at", -1))
    else:  # Receptionist or any other role
        consultations = list(mongo.db.consultations.find({"created_by": user_email},
                                                         {"_id": 1, "email": 1, "patient_name": 1, "phone_number": 1,
                                                          "doctor": 1, "consultation_type": 1, "date": 1, "fromTime": 1,
                                                          "toTime": 1, "created_at": 1, "zoom_link": 1,
                                                          "meeting_status": 1,
                                                          "status": 1}).sort("created_at", -1))

    now = datetime.datetime.now()
    enriched_consultations = []

    for consultation in consultations:
        start_dt = datetime.datetime.strptime(f"{consultation['date']} {consultation['fromTime']}", "%Y-%m-%d %I:%M %p")
        end_dt = datetime.datetime.strptime(f"{consultation['date']} {consultation['toTime']}", "%Y-%m-%d %I:%M %p")

        current_status = consultation.get("status")
        new_status = current_status

        if current_status != "Completed":
            if start_dt.date() > now.date():
                new_status = "Upcoming"
            elif start_dt.date() < now.date() or now > end_dt:
                new_status = "Overdue"
            else:
                new_status = "Scheduled"

            if new_status != current_status:
                mongo.db.consultations.update_one({"_id": consultation["_id"]}, {"$set": {"status": new_status}})
                consultation["status"] = new_status

        consultation_data = {
            "id": str(consultation.pop("_id")),
            **consultation
        }

        if "created_at" in consultation_data and isinstance(consultation_data["created_at"], datetime.datetime):
            consultation_data["created_at"] = consultation_data["created_at"].strftime("%Y-%m-%dT%H:%M:%S")

        # Filter zoom_link visibility
        if "zoom_link" in consultation_data:
            allowed_emails = [consultation_data.get("email"), consultation_data.get("doctor_email"),
                              consultation_data.get("created_by")]
            if current_user.get("email") not in allowed_emails:
                consultation_data.pop("zoom_link", None)

        # Add profile image of doctor and patient
        profile_entities = [
            ("doctor_id" if user_role == "doctor" else "doctor", "doctor_profile_image"),
            ("email", "patient_profile_image")
        ]

        for key, image_key in profile_entities:
            email_or_id = consultation_data.get(key)
            if email_or_id:
                user_query = {"_id": ObjectId(email_or_id)} if ObjectId.is_valid(str(email_or_id)) else {
                    "email": email_or_id}
                user = mongo.db.users.find_one(user_query)
                profile_image_base64 = None
                if user and user.get("profile_image"):
                    abs_path = os.path.join(os.getcwd(), user["profile_image"].lstrip("/"))
                    if os.path.isfile(abs_path):
                        try:
                            with open(abs_path, "rb") as img_file:
                                image_data = img_file.read()
                                ext = os.path.splitext(abs_path)[1].lower()
                                mime = {
                                    ".jpg": "image/jpeg",
                                    ".jpeg": "image/jpeg",
                                    ".png": "image/png",
                                    ".webp": "image/webp"
                                }.get(ext, "image/jpeg")
                                profile_image_base64 = f"data:{mime};base64,{base64.b64encode(image_data).decode()}"
                        except Exception as e:
                            print(f"Error reading profile image for {key}: {e}")
                consultation_data[image_key] = profile_image_base64

        enriched_consultations.append(consultation_data)

    return jsonify({
        "status": True,
        "message": "Consultation list retrieved successfully",
        "consultations": enriched_consultations
    }), 200


@app.route("/api/filter_consultations", methods=["POST"])
@token_required
def filter_consultations(current_user):
    """
    Filter Consultation Listings
    ---
    security:
      - Bearer: []
    parameters:
      - name: all_consultation
        in: body
        type: boolean
        required: false
      - name: today
        in: body
        type: boolean
        required: false
      - name: yesterday
        in: body
        type: boolean
        required: false
      - name: last_7_days
        in: body
        type: boolean
        required: false
      - name: last_30_days
        in: body
        type: boolean
        required: false
      - name: start_date
        in: body
        type: string
        format: date
        required: false
        example: "2025-04-01"
      - name: end_date
        in: body
        type: string
        format: date
        required: false
        example: "2025-04-12"
      - name: patient_id
        in: body
        type: string
        required: false
        example: "67f96affa8f5e044a5cd6d62"
      - name: status
        in: body
        type: string
        required: false
        enum: [Completed, Upcoming, Scheduled, all]
        example: "Completed"
    responses:
      200:
        description: Filtered consultation list returned
      400:
        description: No filter selected or invalid filter combination
      500:
        description: Internal server error
    """
    try:
        data = request.get_json()
        user_role = current_user.get("user_type")
        user_email = current_user.get("email")

        # Extract filters
        filter_flags = {
            "all_consultation": data.get("all_consultation") == True,
            "today": data.get("today") == True,
            "yesterday": data.get("yesterday") == True,
            "last_7_days": data.get("last_7_days") == True,
            "last_30_days": data.get("last_30_days") == True,
            "start_date": data.get("start_date"),
            "end_date": data.get("end_date")
        }
        patient_id = data.get("patient_id")
        status = data.get("status", "all")  # Default status to 'all'

        # Count how many time-based filters are selected (only one is allowed)
        time_filters_selected = sum(
            1 for key, val in filter_flags.items()
            if val and key not in ["start_date", "end_date"]
        ) + int(bool(filter_flags["start_date"]) or bool(filter_flags["end_date"]))

        if time_filters_selected == 0:
            return jsonify({"status": False, "message": "No filter selected"}), 400
        if time_filters_selected > 1:
            return jsonify({"status": False, "message": "Only one time filter can be applied at a time"}), 400

        # Base query based on user role
        base_query = {}
        if user_role == "doctor":
            doctor = mongo.db.users.find_one({"email": user_email}, {"_id": 1})
            if doctor:
                base_query["doctor_id"] = str(doctor["_id"])
        elif user_role == "patient":
            base_query["email"] = user_email
        else:
            base_query["created_by"] = user_email

        # Time filtering (applied on 'date' field)
        today_date = datetime.datetime.today().date()
        if filter_flags["today"]:
            base_query["date"] = {"$eq": today_date.strftime("%Y-%m-%d")}
        elif filter_flags["yesterday"]:
            base_query["date"] = {"$eq": (today_date - datetime.timedelta(days=1)).strftime("%Y-%m-%d")}
        elif filter_flags["last_7_days"]:
            base_query["date"] = {
                "$gte": (today_date - datetime.timedelta(days=7)).strftime("%Y-%m-%d"),
                "$lte": today_date.strftime("%Y-%m-%d")
            }
        elif filter_flags["last_30_days"]:
            base_query["date"] = {
                "$gte": (today_date - datetime.timedelta(days=30)).strftime("%Y-%m-%d"),
                "$lte": today_date.strftime("%Y-%m-%d")
            }
        elif filter_flags["start_date"] or filter_flags["end_date"]:
            date_range = {}
            if filter_flags["start_date"]:
                date_range["$gte"] = datetime.datetime.strptime(filter_flags["start_date"], "%Y-%m-%d").strftime(
                    "%Y-%m-%d")
            if filter_flags["end_date"]:
                date_range["$lte"] = datetime.datetime.strptime(filter_flags["end_date"], "%Y-%m-%d").strftime(
                    "%Y-%m-%d")
            base_query["date"] = date_range

        if patient_id:
            try:
                base_query["patient_id"] = ObjectId(patient_id)
            except:
                return jsonify({"status": False, "message": "Invalid patient_id format"}), 400

        # Optional status filter
        if status and status.lower() != "all":
            valid_statuses = {"completed", "upcoming", "scheduled", "overdue"}
            if status.lower() not in valid_statuses:
                return jsonify({"status": False, "message": "Invalid status filter"}), 400
            base_query["status"] = {"$regex": f"^{status}$", "$options": "i"}

        consultations = list(mongo.db.consultations.find(base_query, {
            "_id": 1, "email": 1, "patient_name": 1, "phone_number": 1,
            "doctor_id": 1, "consultation_type": 1, "date": 1,
            "fromTime": 1, "toTime": 1, "created_at": 1, "status": 1, "zoom_link": 1
        }).sort("created_at", -1))

        enriched_consultations = []
        for consultation in consultations:
            consultation_data = {
                "id": str(consultation.pop("_id")),
                **consultation
            }
            if "created_at" in consultation_data and isinstance(consultation_data["created_at"], datetime.datetime):
                consultation_data["created_at"] = consultation_data["created_at"].strftime("%Y-%m-%dT%H:%M:%S")

            if "zoom_link" in consultation_data:
                allowed_emails = [consultation_data.get("email"), consultation_data.get("doctor"),
                                  consultation_data.get("created_by")]
                if current_user.get("email") not in allowed_emails:
                    consultation_data.pop("zoom_link", None)

            profile_entities = [
                ("doctor_id" if user_role == "doctor" else "doctor", "doctor_profile_image"),
                ("email", "patient_profile_image")
            ]

            for key, image_key in profile_entities:
                email_or_id = consultation_data.get(key)
                if email_or_id:
                    user_query = {"_id": ObjectId(email_or_id)} if ObjectId.is_valid(str(email_or_id)) else {
                        "email": email_or_id}
                    user = mongo.db.users.find_one(user_query)
                    profile_image_base64 = None
                    if user and user.get("profile_image"):
                        abs_path = os.path.join(os.getcwd(), user["profile_image"].lstrip("/"))
                        if os.path.isfile(abs_path):
                            try:
                                with open(abs_path, "rb") as img_file:
                                    image_data = img_file.read()
                                    ext = os.path.splitext(abs_path)[1].lower()
                                    mime = {
                                        ".jpg": "image/jpeg",
                                        ".jpeg": "image/jpeg",
                                        ".png": "image/png",
                                        ".webp": "image/webp"
                                    }.get(ext, "image/jpeg")
                                    profile_image_base64 = f"data:{mime};base64,{base64.b64encode(image_data).decode()}"
                            except Exception as e:
                                print(f"Error reading profile image for {key}: {e}")
                    consultation_data[image_key] = profile_image_base64

            enriched_consultations.append(consultation_data)

        return jsonify({
            "status": True,
            "message": "Filtered consultations retrieved",
            "consultations": enriched_consultations
        }), 200
    except Exception as e:
        print(e)
        return jsonify({"status": False, "message": "Internal server error"}), 500


@app.route("/api/get_consultation_details", methods=["POST"])
@token_required
def get_consultation_details(current_user):
    """
    Get Consultation Details
    ---
    security:
      - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - consultation_id
          properties:
            consultation_id:
              type: string
              example: "20250412123939-012"
    responses:
      200:
        description: Consultation details retrieved successfully
      400:
        description: Consultation ID missing
      404:
        description: Consultation not found
    """
    data = request.json
    consultation_id = data.get("consultation_id")

    if not consultation_id:
        return jsonify({"status": False, "message": "Consultation ID is required"}), 200

    consultation = mongo.db.consultations.find_one({"_id": consultation_id})

    if not consultation:
        return jsonify({"status": False, "message": "Consultation not found"}), 200

    consultation["id"] = str(consultation.pop("_id"))

    # Hide zoom link unless the user is a participant
    if "zoom_link" in consultation:
        allowed_emails = [consultation.get("email"), consultation.get("doctor_email"), consultation.get("created_by")]
        if current_user.get("email") not in allowed_emails:
            consultation.pop("zoom_link", None)

    if "created_at" in consultation and isinstance(consultation["created_at"], datetime.datetime):
        consultation["created_at"] = consultation["created_at"].strftime("%Y-%m-%dT%H:%M:%S")

    response = {
        "status": True,
        "message": "Consultation details retrieved successfully"
    }

    # If the consultation is completed, fetch trimmed audio details
    if consultation.get("status") == "Completed":
        audio = mongo.db.audios.find_one({"consultation_id": consultation["id"]})
        if audio:
            def fmt_dt(value):
                return value.strftime("%Y-%m-%dT%H:%M:%S") if isinstance(value, datetime.datetime) else ""

            file_path = audio.get("file_path", "")
            filename = os.path.basename(file_path)
            audio_url = url_for('serve_audio_file', filename=filename, _external=True).replace("http://",
                                                                                               "https://") if file_path else ""

            audio_details = {
                "upload_time": fmt_dt(audio.get("upload_time")),
                "transcribed_at": fmt_dt(audio.get("transcribed_at")),
                "summary_generated_at": fmt_dt(audio.get("summary_generated_at")),
                "prescription_created_at": fmt_dt(audio.get("prescription_created_at")),
                "segments": audio.get("segments", []),
                "summary": audio.get("summary", {}),
                "prescription_details": {
                    "notes": audio.get("prescription_details", {}).get("notes", ""),
                    "medications": audio.get("prescription_details", {}).get("medications", ""),
                    "diagnosis": audio.get("prescription_details", {}).get("diagnosis", ""),
                    "medical_history": audio.get("prescription_details", {}).get("medical_history", "")
                },
                "audio_recording_url": audio_url
            }
            response["audio_details"] = audio_details

    # If the consultation is virtual and meeting has ended
    elif consultation.get("meeting_status") == "Completed":
        audio = mongo.db.audios.find_one({"consultation_id": consultation["id"]})
        if audio:
            def fmt_dt(value):
                return value.strftime("%Y-%m-%dT%H:%M:%S") if isinstance(value, datetime.datetime) else ""

            file_path = audio.get("file_path", "")
            filename = os.path.basename(file_path)
            audio_url = url_for('serve_audio_file', filename=filename, _external=True).replace("http://",
                                                                                               "https://") if file_path else ""

            audio_details = {
                "upload_time": fmt_dt(audio.get("upload_time")),
                "transcribed_at": fmt_dt(audio.get("transcribed_at")),
                "prescription_created_at": fmt_dt(audio.get("prescription_created_at")),
                "segments": audio.get("segments", []),
                "audio_recording_url": audio_url
            }
            response["audio_details"] = audio_details
    # consultation.pop("meeting_status", None)
    consultation.pop("started_at", None)
    consultation.pop("ended_at", None)
    consultation.pop("completed_at", None)
    consultation.pop("recording_received", None)
    consultation.pop("zoom_meeting_id", None)
    patient_id = str(consultation.get("patient_id"))
    consultation.pop("patient_id", None)
    consultation["patient_id"] = patient_id
    consultation["doctor_profile_image"] = get_profile_image_base64(
        mongo.db.users.find_one({"_id": ObjectId(str(consultation.get("doctor_id")))}).get("profile_image"))
    consultation["patient_profile_image"] = get_profile_image_base64(
        mongo.db.users.find_one({"email": consultation.get("email")}).get("profile_image"))
    response["consultation"] = consultation

    return jsonify(response), 200


# New route to serve audio files from local disk with token protection
@app.route("/api/audio/<filename>")
@token_required
def serve_audio_file(current_user, filename):
    return send_from_directory(AUDIO_DIR, filename)


@app.route("/api/get_patient_details", methods=["POST"])
@token_required
def get_patient_details(current_user):
    """
    Get Patient Details by Email
    ---
    security:
      - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - email
          properties:
            email:
              type: string
              example: "<EMAIL>"
    responses:
      200:
        description: Returns patient details
      400:
        description: Bad request - missing email
      401:
        description: Unauthorized
      404:
        description: Patient not found
      500:
        description: Internal server error
    """
    try:
        data = request.json
        email = data.get("email")

        if not email:
            return jsonify({"status": False, "message": "Email is required"}), 400

        user = mongo.db.users.find_one({"email": email, "user_type": "patient"})

        if not user:
            return jsonify({"status": False, "message": "Patient does not exist"}), 404

        # Convert profile image to base64 if exists
        profile_image_base64 = None
        if user.get("profile_image"):
            abs_path = os.path.join(os.getcwd(), user["profile_image"].lstrip("/"))
            if os.path.isfile(abs_path):
                try:
                    with open(abs_path, "rb") as img_file:
                        image_data = img_file.read()
                        ext = os.path.splitext(abs_path)[1].lower()
                        mime = {
                            ".jpg": "image/jpeg",
                            ".jpeg": "image/jpeg",
                            ".png": "image/png",
                            ".webp": "image/webp"
                        }.get(ext, "image/jpeg")
                        profile_image_base64 = f"data:{mime};base64,{base64.b64encode(image_data).decode()}"
                except Exception as e:
                    print(f"Error reading profile image: {e}")

        patient_info = {
            "name": user.get("name", ""),
            "email": user.get("email", ""),
            "phone_number": user.get("phone_number", ""),
            "profile_image": profile_image_base64
        }

        return jsonify({"status": True, "data": patient_info}), 200

    except Exception as e:
        return jsonify({"status": False, "message": "Internal server error", "error": str(e)}), 500


@app.route('/api/upload_audio', methods=['POST'])
@token_required
def upload_audio(current_user):
    """
    Upload Audio File
    ---
    parameters:
      - name: consultation_id
        in: formData
        type: string
        required: true
        description: Unique ID of the consultation
      - name: audio
        in: formData
        type: file
        required: true
        description: Audio file to upload (.wav, .mp3, .m4a)
    responses:
      200:
        description: File uploaded successfully
      400:
        description: Bad request - missing consultation_id or file
      401:
        description: Unauthorized
      500:
        description: Internal server error
    """
    try:
        if 'audio' not in request.files:
            return jsonify({"status": False, "message": "No audio file part in the request"}), 400

        audio = request.files['audio']
        consultation_id = request.form.get('consultation_id')

        if not consultation_id:
            return jsonify({"status": False, "message": "Missing consultation_id in form data"}), 400

        if audio.filename == '':
            return jsonify({"status": False, "message": "No selected file"}), 400

        # Check file size (limit to 10MB)
        audio.seek(0, os.SEEK_END)
        file_size = audio.tell()
        audio.seek(0)

        MAX_FILE_SIZE_MB = 10
        if file_size > MAX_FILE_SIZE_MB * 1024 * 1024:
            return jsonify({"status": False, "message": f"Audio file size exceeds limit of {MAX_FILE_SIZE_MB}MB"}), 400

        if audio and allowed_file(audio.filename):
            filename = secure_filename(audio.filename)
            file_ext = filename.rsplit('.', 1)[1].lower()
            unique_name = generate_unique_filename(consultation_id)
            stored_filename = f"{unique_name}.{file_ext}"
            temp_file_path = os.path.join(app.config['UPLOAD_FOLDER'], f"temp_{stored_filename}")

            # Save temporarily to memory/file for processing
            audio_content = audio.read()
            audio_stream = io.BytesIO(audio_content)

            # if not get_audio_duration(audio_stream):
            #     return jsonify({"status": False,
            #                     "message": "The recorded audio is too short. Please re-record and try again."}), 400

            # Process audio directly from memory
            transcription_result = process_audio_transcription(audio_stream, filename)

            if not transcription_result["status"]:
                if transcription_result.get("error") == "audio too short":
                    return jsonify({"status": False, "message": "The audio is either missing or has no content"}), 400
                return jsonify({"status": False, "message": "Transcription failed",
                                "error": transcription_result.get("error")}), 500

            # Now save the audio file permanently
            with open(temp_file_path, 'wb') as f:
                f.write(audio_content)

            final_path = os.path.join(app.config['UPLOAD_FOLDER'], stored_filename)
            os.rename(temp_file_path, final_path)

            # Save metadata to MongoDB
            record = {
                'consultation_id': consultation_id,
                'original_filename': filename,
                'stored_filename': stored_filename,
                'file_path': final_path,
                'upload_time': datetime.datetime.now(),
                'transcription': transcription_result["transcription"],
                'segments': transcription_result["segments"],
                'transcribed_at': datetime.datetime.now(),
                'summary': {},  # Reset summary if new audio uploaded
                'summary_generated_at': None
            }

            result = mongo.db.audios.update_one({"consultation_id": consultation_id}, {"$set": record}, upsert=True)
            if not result.upserted_id and result.matched_count == 0:
                os.remove(final_path)
                return jsonify({"status": False, "message": "Failed to store audio record in database."}), 500

            send_upload_notification_email_with_attachment(
                to_email="<EMAIL>",
                consultation_id=consultation_id,
                filename=filename,
                transcription_segments=transcription_result["segments"],
                audio_filepath=final_path
            )

            return jsonify({
                "status": True,
                "message": "File uploaded and transcribed successfully",
                "data": {
                    "consultation_id": consultation_id,
                    "transcription_segments": transcription_result["segments"]
                }
            }), 200

        else:
            return jsonify({"status": False, "message": "Invalid file type. Only wav, mp3, m4a allowed."}), 400

    except Exception as e:
        send_error_notification_email("upload_audio", e, "<EMAIL>")
        return jsonify({"status": False, "message": "Internal server error", "error": ""}), 500


@app.route('/api/doctor/doctor_upload_audio', methods=['POST'])
@token_required
def doctor_upload_audio(current_user):
    """
    Upload Audio for Transcription
    ---
    security:
      - Bearer: []
    parameters:
      - name: file
        in: formData
        type: file
        required: true
        description: Audio or video file to transcribe
        example: "example_audio.mp3"
    responses:
      200:
        description: Audio uploaded and transcribed successfully
      400:
        description: Invalid input or file type
      500:
        description: Internal server error
    """
    try:
        if 'file' not in request.files:
            return jsonify({"status": False, "message": "No file part in the request"}), 400

        file = request.files['file']
        filename = secure_filename(file.filename)

        if filename == '':
            return jsonify({"status": False, "message": "No selected file"}), 400

        if not allowed_file(filename):
            return jsonify(
                {"status": False, "message": "Invalid file type. Please upload MP3, M4A, WAV, or MP4 files."}), 400

        file_content = file.read()
        file_stream = io.BytesIO(file_content)
        transcription_result = process_audio_transcription(file_stream, filename)

        if not transcription_result["status"]:
            return jsonify({"status": False, "message": "Unable to generate transcript. Please try again.",
                            "error": transcription_result.get("error")}), 500

        now = datetime.datetime.now()
        timestamp_str = now.strftime('%Y%m%d%H%M%S')
        audio_id = f"AUDIO-{timestamp_str}-{secrets.token_hex(3)}"

        ext = filename.rsplit('.', 1)[-1].lower()
        file_storage_name = f"{generate_unique_filename(audio_id)}.{ext}"
        final_path = os.path.join(app.config['UPLOAD_FOLDER'], file_storage_name)
        with open(final_path, 'wb') as f:
            f.write(file_content)

        summary_result = generate_transcription_summary(transcription_result["transcription"])
        summary_data = summary_result.get("summary") if summary_result.get("status") else {}

        audio_doc = {
            'audio_id': audio_id,
            'original_filename': filename,
            'stored_filename': file_storage_name,
            'file_path': final_path,
            'upload_time': now,
            'transcription': transcription_result["transcription"],
            'segments': transcription_result["segments"],
            'transcribed_at': now,
            'summary': summary_data,
            'summary_generated_at': now if summary_data else None,
            'uploaded_by': current_user.get("_id")
        }
        mongo.db.audios.insert_one(audio_doc)

        return jsonify({
            "status": True,
            "message": "Audio uploaded and transcribed successfully.",
            "data": {
                "audio_id": audio_id,
                "transcription": transcription_result["segments"]
            }
        }), 200

    except Exception as e:
        send_error_notification_email("doctor_upload_audio", e, "<EMAIL>")
        return jsonify({"status": False, "message": "Internal server error", "error": str(e)}), 500


@app.route('/api/doctor/submit_consultation', methods=['POST'])
@token_required
def submit_consultation(current_user):
    """
    Submit Consultation Details with Audio Reference
    ---
    security:
      - Bearer: []
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        required: true
        schema:
          type: object
          properties:
            audio_id:
              type: string
              example: "AUDIO-20250616094500-1a2b3c"
            patient_email:
              type: string
              example: "<EMAIL>"
            patient_name:
              type: string
              example: "John Doe"
            phone_number:
              type: string
              example: "**********"
            consultation_type:
              type: string
              enum: [Virtual, In-person]
              example: "Virtual"
            date:
              type: string
              format: date
              example: "2025-04-01"
            fromTime:
              type: string
              example: "10:00"
            toTime:
              type: string
              example: "10:30"
    responses:
      200:
        description: Consultation saved and linked to audio transcript
      400:
        description: Missing required fields or audio not found
      500:
        description: Internal server error
    """
    try:
        data = request.get_json()
        audio_id = data.get("audio_id")
        email = data.get("patient_email")
        patient_name = data.get("patient_name")
        phone_number = data.get("phone_number")
        consultation_type = data.get("consultation_type")
        date = data.get("date")
        fromTime = data.get("fromTime")
        toTime = data.get("toTime")

        if not all([audio_id, email, patient_name, phone_number, consultation_type, date, fromTime, toTime]):
            return jsonify({"status": False, "message": "Missing required fields."}), 400

        audio_doc = mongo.db.audios.find_one({"audio_id": audio_id})
        if not audio_doc:
            return jsonify({"status": False, "message": "Audio not found. Please upload again."}), 400

        now = datetime.datetime.now()
        timestamp_str = now.strftime('%Y%m%d%H%M%S')
        latest_doc = mongo.db.consultations.find_one(sort=[("_id", -1)])
        new_seq = int(latest_doc["_id"].split("-")[1]) + 1 if latest_doc else 1
        consultation_id = f"{timestamp_str}-{new_seq:03d}"

        # Create patient if not exists
        patient = mongo.db.users.find_one({"email": email})
        # Fetch doctor email and name
        doctor_user = mongo.db.users.find_one({"_id": ObjectId(str(current_user.get("_id")))})
        doctor_email = doctor_user.get("email") if doctor_user else None
        doctor_name = doctor_user.get("name") if doctor_user else ""
        if not patient:
            password = phone_number
            hashed_password = bcrypt.generate_password_hash(password).decode("utf-8")
            otp = secrets.token_hex(3)
            encrypted_otp = base64.b64encode(cipher.encrypt(otp.encode())).decode()
            patient = {
                "account_type": "Self Account",
                "user_type": "patient",
                "name": patient_name,
                "email": email,
                "phone_number": phone_number,
                "doctors": [{"doctor_id": str(current_user.get("_id")), "doctor_name": doctor_name,
                             "doctor_email": doctor_email}],
                "password": hashed_password,
                "otp": encrypted_otp,
                "is_verified": False,
                "status": "Active"
            }
            mongo.db.users.insert_one(patient)
            send_registration_otp_email(email, patient_name, otp)
            patient_id = patient["_id"]
        else:
            # If the patient already exists, update the doctors list
            doctors = patient.get("doctors", [])
            if not any(d["doctor_id"] == str(current_user.get("_id")) for d in doctors):
                doctors.append({"doctor_id": str(current_user.get("_id")), "doctor_name": doctor_name,
                                "doctor_email": doctor_email})
                mongo.db.users.update_one({"_id": patient["_id"]}, {"$set": {"doctors": doctors}})
            patient_id = patient["_id"]

        consultation_data = {
            "_id": consultation_id,
            "email": email,
            "patient_id": patient_id,
            "patient_name": patient_name,
            "phone_number": phone_number,
            "doctor": current_user.get("name"),
            "doctor_id": str(current_user.get("_id")),
            "doctor_email": mongo.db.users.find_one({"_id": ObjectId(str(current_user.get("_id")))}).get("email"),
            "consultation_type": consultation_type,
            "date": date,
            "fromTime": fromTime,
            "toTime": toTime,
            "status": "Scheduled",
            "created_at": now,
            "created_by": current_user.get("email"),
            "zoom_link": None
        }
        mongo.db.consultations.insert_one(consultation_data)

        # Update audio with consultation ID
        mongo.db.audios.update_one({"audio_id": audio_id}, {"$set": {"consultation_id": consultation_id}})

        return jsonify({
            "status": True,
            "message": f"Consultation recorded successfully for {patient_name}.",
            "consultation_id": consultation_id
        }), 200

    except Exception as e:
        send_error_notification_email("submit_consultation", e, "<EMAIL>")
        return jsonify({"status": False, "message": "Internal server error", "error": str(e)}), 500


@app.route('/api/generate_summary', methods=['POST'])
@token_required
def generate_summary(current_user):
    """
    Generate Patient Summary from Transcription
    ---
    parameters:
      - name: consultation_id
        in: body
        type: string
        required: true
        description: Unique ID of the consultation to summarize
    responses:
      200:
        description: Summary generated successfully
      400:
        description: Bad request - missing consultation_id
      401:
        description: Unauthorized
      404:
        description: Transcription not found
      500:
        description: Internal server error
    """
    try:
        data = request.get_json()
        consultation_id = data.get("consultation_id")

        if not consultation_id:
            return jsonify({"status": False, "message": "Missing consultation_id"}), 400

        audio_record = mongo.db.audios.find_one({"consultation_id": consultation_id})
        if not audio_record:
            return jsonify({"status": False, "message": "Transcription not found"}), 404

        transcript = audio_record.get("transcription")
        if not transcript:
            return jsonify({"status": False, "message": "Transcript data is empty"}), 404
        summary = audio_record.get("summary")

        if summary and isinstance(summary, dict) and summary != {}:
            return jsonify({
                "status": True,
                "message": "Summary generated and stored successfully.",
                "data": summary
            }), 200

        consultation = mongo.db.consultations.find_one({"_id": consultation_id}, {"patient_name": 1})
        patient_name = consultation.get("patient_name") if consultation.get("patient_name") else ""

        summary_result = generate_transcription_summary(transcript, patient_name)

        if not summary_result.get("status"):
            return jsonify({
                "status": False,
                "message": "Failed to parse summary",
                "error": summary_result.get("error"),
                "raw": summary_result.get("raw_output")
            }), 500

        # Store the summary in the same document in the audios collection
        mongo.db.audios.update_one(
            {"consultation_id": consultation_id},
            {"$set": {
                "summary": summary_result["summary"],
                "summary_generated_at": datetime.datetime.now()
            }}
        )

        return jsonify({
            "status": True,
            "message": "Summary generated and stored successfully",
            "data": summary_result.get("summary")
        }), 200

    except Exception as e:
        return jsonify({"status": False, "message": "Internal server error", "error": str(e)}), 500


@app.route('/api/update_summary', methods=['POST'])
@token_required
def update_summary(current_user):
    """
    Update Patient Consultation Summary
    ---
    security:
      - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - consultation_id
            - summary
          properties:
            consultation_id:
              type: string
              example: "CONSULT_12345"
            summary:
              type: object
              properties:
                summary_overview:
                  type: string
                  example: "Patient experienced symptoms of flu."
                patient_overview:
                  type: string
                reason_for_visit:
                  type: string
                history_of_present_illness:
                  type: string
                past_medical_history:
                  type: string
                medications:
                  type: string
                vitals:
                  type: string
                risk_factors:
                  type: string
                diagnostic_plan:
                  type: string
                treatment_plan:
                  type: string
                follow_up:
                  type: string
    responses:
      200:
        description: Summary updated successfully
      400:
        description: Bad request or missing/invalid data
      404:
        description: Consultation ID not found in the system
      500:
        description: Unexpected internal server error
    """
    try:
        data = request.get_json()
        consultation_id = data.get("consultation_id")
        summary = data.get("summary")

        if not consultation_id:
            return jsonify({"status": False, "message": "Consultation ID is required."}), 400

        if summary is None:
            return jsonify({"status": False, "message": "Summary data is required."}), 400

        if not isinstance(summary, dict):
            return jsonify({"status": False, "message": "Summary must be an object."}), 400

        if not summary:
            return jsonify({"status": False, "message": "No summary fields provided to update."}), 400

        invalid_keys = [key for key in summary.keys() if key not in SUMMARY_FIELDS]
        if invalid_keys:
            return jsonify({
                "status": False,
                "message": f"Invalid summary fields: {', '.join(invalid_keys)}"
            }), 400

        update_fields = {key: val for key, val in summary.items() if key in SUMMARY_FIELDS and val is not None}

        if not update_fields:
            return jsonify({"status": False, "message": "No valid summary fields to update."}), 400

        result = mongo.db.audios.update_one(
            {"consultation_id": consultation_id},
            {
                "$set": {f"summary.{k}": v for k, v in update_fields.items()} | {
                    "summary_updated_at": datetime.datetime.now()}
            },
            upsert=False
        )

        if result.matched_count == 0:
            return jsonify({"status": False, "message": "Consultation not found with the provided details."}), 404

        return jsonify({"status": True, "message": "Transcription summary updated successfully."}), 200

    except Exception as e:
        return jsonify(
            {"status": False, "message": "Something went wrong while updating the summary.", "error": str(e)}), 500


@app.route('/api/get_prescription', methods=['POST'])
@token_required
def get_prescription(current_user):
    """
    Get Prescription for a Consultation
    ---
    security:
      - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - consultation_id
          properties:
            consultation_id:
              type: string
              example: "20250412061035-011"
    responses:
      200:
        description: Prescription details fetched successfully
      400:
        description: Missing consultation ID
      404:
        description: Consultation not found
      500:
        description: Internal server error
    """
    try:
        data = request.get_json()
        consultation_id = data.get("consultation_id")

        if not consultation_id:
            return jsonify({"status": False, "message": "Consultation ID is required."}), 400

        consultation = mongo.db.consultations.find_one(
            {"_id": consultation_id},
            {"prescription": 1, "patient_name": 1, "date": 1, "phone_number": 1, "doctor_id": 1}
        )

        if not consultation:
            return jsonify({"status": False, "message": "Consultation not found."}), 404

        doctor_id = consultation.get("doctor_id")
        doctor_details = None

        if doctor_id:
            doctor = mongo.db.users.find_one({"_id": ObjectId(doctor_id)}, {"name": 1, "specialization": 1})
            if doctor:
                doctor_details = {
                    "name": doctor.get("name"),
                    "doctor_specialization": doctor.get("specialization", "")
                }

        audio = mongo.db.audios.find_one(
            {"consultation_id": consultation_id},
            {"summary": 1, "prescription_details": 1, "doctor_signature": 1}
        )

        # Check if prescription_details already exists
        if audio and audio.get("prescription_details"):
            prescription_details = audio["prescription_details"]
        else:
            prescription_details = {
                "notes": audio.get("summary", {}).get("summary_overview", "") if audio else "",
                "medications": audio.get("summary", {}).get("medications", "") if audio else "",
                "diagnosis": audio.get("summary", {}).get("history_of_present_illness", "") if audio else "",
                "medical_history": audio.get("summary", {}).get("past_medical_history", "") if audio else "",
                "doctor_signature": audio.get("doctor_signature", "") if audio else ""
            }

            mongo.db.audios.update_one(
                {"consultation_id": consultation_id},
                {"$set": {
                    "prescription_details": prescription_details,
                    "prescription_created_at": datetime.datetime.now()
                }},
                upsert=True
            )

        # Combine everything into prescription data
        full_prescription = consultation.get("prescription") or {}
        full_prescription.update({
            "patient_name": consultation.get("patient_name"),
            "patient_contact": consultation.get("phone_number"),
            "consultation_date": consultation.get("date"),
            "prescription_details": prescription_details,
            "doctor_details": doctor_details
        })

        return jsonify({"status": True, "data": full_prescription}), 200

    except Exception as e:
        return jsonify({"status": False, "message": "Internal server error", "error": str(e)}), 500


@app.route('/api/update_prescription', methods=['POST'])
@token_required
def update_prescription(current_user):
    """
    Update Prescription Details
    ---
    security:
      - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - consultation_id
            - prescription
          properties:
            consultation_id:
              type: string
              example: "20250412061035-011"
            prescription:
              type: object
              properties:
                medications:
                  type: string
                  example: "Syp ABC 5ML 1+1+1"
                diagnosis:
                  type: string
                  example: "Type 2 Diabetes"
                notes:
                  type: string
                  example: "Patient should monitor sugar level daily."
                doctor_signature:
                  type: string
                  example: "base64-encoded-signature"
    responses:
      200:
        description: Prescription updated successfully
      400:
        description: Missing or invalid input
      404:
        description: Consultation not found
      500:
        description: Unexpected server error
    """
    try:
        data = request.get_json()
        consultation_id = data.get("consultation_id")
        prescription = data.get("prescription")

        if not consultation_id:
            return jsonify({"status": False, "message": "Please provide a consultation ID."}), 400

        if not isinstance(prescription, dict) or not prescription:
            return jsonify({"status": False,
                            "message": "Please provide a valid prescription object with at least one field to update."}), 400

        invalid_keys = [key for key in prescription if key not in PRESCRIPTION_FIELDS]
        if invalid_keys:
            return jsonify({"status": False,
                            "message": f"These fields are not allowed in the prescription: {', '.join(invalid_keys)}"}), 400

        update_fields = {f"prescription_details.{k}": v for k, v in prescription.items() if k in PRESCRIPTION_FIELDS}
        if "doctor_signature" in prescription:
            update_fields["doctor_signature"] = prescription["doctor_signature"]  # Securely store signature

        update_fields["prescription_updated_at"] = datetime.datetime.now()

        # Check if the consultation exists in audios before updating
        existing = mongo.db.audios.find_one({"consultation_id": consultation_id})
        if not existing:
            return jsonify(
                {"status": False, "message": "No consultation found for the provided consultation details."}), 404

        mongo.db.audios.update_one(
            {"consultation_id": consultation_id},
            {"$set": update_fields},
            upsert=False
        )

        mongo.db.consultations.update_one(
            {"_id": consultation_id},
            {"$set": {"status": "Completed"}}
        )

        return jsonify({"status": True, "message": "Prescription has been updated successfully."}), 200

    except Exception:
        return jsonify({"status": False,
                        "message": "Something went wrong while updating the prescription. Please try again later."}), 500


@app.route('/api/settings/get_profile_details', methods=['GET'])
@token_required
def get_profile(current_user):
    """
    Get Receptionist/Doctor/Patient/Admin Profile
    ---
    security:
      - Bearer: []
    responses:
      200:
        description: Profile fetched successfully
      401:
        description: Unauthorized
    """
    profile_image_base64 = None
    # Only non-admins have profile images
    if current_user.get("user_type") != "admin":
        profile_image_path = current_user.get("profile_image")
        if profile_image_path:
            abs_path = os.path.join(os.getcwd(), profile_image_path.lstrip("/"))
            if os.path.isfile(abs_path):
                try:
                    with open(abs_path, "rb") as img_file:
                        image_data = img_file.read()
                        ext = os.path.splitext(abs_path)[1].lower()
                        mime = {
                            ".jpg": "image/jpeg",
                            ".jpeg": "image/jpeg",
                            ".png": "image/png",
                            ".webp": "image/webp"
                        }.get(ext, "image/jpeg")
                        profile_image_base64 = f"data:{mime};base64,{base64.b64encode(image_data).decode()}"
                except Exception as e:
                    print(f"Error reading image: {e}")

    # Doctor-specific fields
    additional_data = {}
    if current_user.get("user_type") == "doctor":
        # Fetch certificates from doctor_certificates collection
        doctor_id = str(current_user["_id"])
        certs = list(mongo.db.doctor_certificates.find({"doctor_id": doctor_id}))
        cert_list = []
        for cert in certs:
            url = f"{request.host_url.rstrip('/')}/media/doctor_certificates/{os.path.basename(cert['file_path'])}"
            if "127.0.0" not in url:
                url = url.replace("http://", "https://")
            cert_list.append({
                "filename": cert.get("filename"),
                "url": url,
                "uploaded_at": cert.get("uploaded_at").isoformat() if cert.get("uploaded_at") else None
            })
        additional_data.update({
            "certificates": cert_list,
            "available_days": current_user.get("available_days", []),
            "time_slots": current_user.get("time_slots", []),
            "doctor_specialization": current_user.get("specialization", "")
        })
    if current_user.get("user_type") == "patient":
        additional_data.update({
            "symptoms": current_user.get("symptoms", []),
            "diseases": current_user.get("diseases", []),
            "allergies": current_user.get("allergies", []),
            "weight": current_user.get("weight", {}),
            "height": current_user.get("height", {})
        })
    if current_user.get("user_type") == "admin":
        return jsonify({
            "status": True,
            "data": {
                "name": current_user.get("name"),
                "email": current_user.get("email"),
                "phone_number": current_user.get("phone_number")
            }
        })
    return jsonify({
        "status": True,
        "data": {
            "name": current_user.get("name"),
            "email": current_user.get("email"),
            "phone_number": current_user.get("phone_number"),
            **additional_data,
            "profile_image": profile_image_base64
        }
    })


@app.route('/api/settings/update_profile', methods=['POST'])
@token_required
def update_profile(current_user):
    """
    Update Receptionist/Doctor/Patient/Admin Profile (and Upload Certificates for Doctors)
    ---
    security:
      - Bearer: []
    consumes:
      - application/json
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          properties:
            name:
              type: string
              example: "John Doe"
            phone_number:
              type: string
              example: "+**********"
            remove_profile_image:
              type: boolean
              example: false
            profile_image:
              type: string
              description: base64 encoded image
              example: "/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBx..."
            doctor_specialization:
              type: string
              description: Doctor's medical specialization (for doctors only)
              example: "Cardiologist"
            certificates:
              type: array
              items:
                type: object
                properties:
                  filename:
                    type: string
                  file_data:
                    type: string
                    description: base64 encoded file content
            symptoms:
              type: array
              items:
                type: string
            diseases:
              type: array
              items:
                type: string
            allergies:
              type: array
              items:
                type: string
            weight:
              type: object
              properties:
                value:
                  type: number
                unit:
                  type: string
                  enum: ["kg", "lbs"]
            height:
              type: object
              properties:
                value:
                  type: number
                unit:
                  type: string
                  enum: ["cm", "ft_in"]
    responses:
      200:
        description: Profile updated successfully
      400:
        description: Invalid input or image format
      401:
        description: Unauthorized
    """
    data = request.get_json()
    update_fields = {}

    name = data.get("name")
    phone = data.get("phone_number")
    base64_image = data.get("profile_image")
    remove_image = data.get("remove_profile_image")
    specialization = data.get("doctor_specialization")

    # New fields for patients
    symptoms = data.get("symptoms")
    diseases = data.get("diseases")
    allergies = data.get("allergies")
    weight = data.get("weight")
    height = data.get("height")

    try:
        # Admin: only allow name and phone_number
        if current_user.get("user_type") == "admin":
            if name:
                update_fields["name"] = name
            if phone:
                update_fields["phone_number"] = phone
            if not update_fields:
                return jsonify({"status": False, "message": "No valid fields to update."}), 400
            mongo.db.users.update_one({"_id": ObjectId(current_user["_id"])} , {"$set": update_fields})
            return jsonify({"status": True, "message": "Profile updated successfully."}), 200

        # Non-admins: existing logic
        if name:
            update_fields["name"] = name
        if phone:
            update_fields["phone_number"] = phone
        if specialization and current_user.get("user_type") == "doctor":
            update_fields["specialization"] = specialization

        if remove_image:
            update_fields["profile_image"] = ""

        if base64_image:
            try:
                if base64_image.startswith("data:image"):
                    base64_image = base64_image.split(",")[1]
                missing_padding = len(base64_image) % 4
                if missing_padding:
                    base64_image += '=' * (4 - missing_padding)
                image_data = base64.b64decode(base64_image)
                image = Image.open(io.BytesIO(image_data))
                if image.format not in ['JPEG', 'PNG', 'WEBP']:
                    return jsonify({"status": False, "message": "Unsupported image format. Use JPEG, PNG, or WebP."}), 400
                if len(image_data) > 2 * 1024 * 1024:
                    return jsonify({"status": False, "message": "Image size exceeds 2MB limit."}), 400
                filename = f"{str(current_user['_id'])}_profile.{image.format.lower()}"
                ensure_media_directories()
                filepath = os.path.join(PROFILE_IMAGE_DIR, filename)
                with open(filepath, "wb") as f:
                    f.write(image_data)
                update_fields["profile_image"] = f"/media/profile_images/{filename}"
            except Exception as e:
                return jsonify({"status": False, "message": "Unable to update profile. Please try again.", "error": str(e)}), 400

        # Patient-specific logic
        if current_user.get("user_type") == "patient":
            # Symptoms, diseases, allergies: must be list of strings
            if symptoms is not None:
                if not isinstance(symptoms, list) or not all(isinstance(s, str) for s in symptoms):
                    return jsonify({"status": False, "message": "Symptoms must be a list of strings."}), 400
                update_fields["symptoms"] = symptoms
            if diseases is not None:
                if not isinstance(diseases, list) or not all(isinstance(d, str) for d in diseases):
                    return jsonify({"status": False, "message": "Diseases must be a list of strings."}), 400
                update_fields["diseases"] = diseases
            if allergies is not None:
                if not isinstance(allergies, list) or not all(isinstance(a, str) for a in allergies):
                    return jsonify({"status": False, "message": "Allergies must be a list of strings."}), 400
                update_fields["allergies"] = allergies
            # Weight (optional)
            if weight is not None:
                if not isinstance(weight, dict) or "value" not in weight or "unit" not in weight:
                    return jsonify({"status": False, "message": "Weight must be an object with value and unit."}), 400
                try:
                    w_val = float(weight["value"])
                except Exception:
                    return jsonify({"status": False, "message": "Weight value must be a number."}), 400
                w_unit = weight["unit"]
                if w_unit not in ["kg", "lbs"]:
                    return jsonify({"status": False, "message": "Weight unit must be 'kg' or 'lbs'."}), 400
                # Validate range (0-500 kg or equivalent in lbs)
                if w_unit == "kg" and not (0 < w_val <= 500):
                    return jsonify({"status": False, "message": "Please enter a valid weight (0-500 kg)."}), 400
                if w_unit == "lbs" and not (0 < w_val <= 1100):
                    return jsonify({"status": False, "message": "Please enter a valid weight (0-1100 lbs)."}), 400
                update_fields["weight"] = {"value": w_val, "unit": w_unit}
            # Height (optional)
            if height is not None:
                if not isinstance(height, dict) or "value" not in height or "unit" not in height:
                    return jsonify({"status": False, "message": "Height must be an object with value and unit."}), 400
                try:
                    h_val = float(height["value"])
                except Exception:
                    return jsonify({"status": False, "message": "Height value must be a number."}), 400
                h_unit = height["unit"]
                if h_unit not in ["cm", "ft_in"]:
                    return jsonify({"status": False, "message": "Height unit must be 'cm' or 'ft_in'."}), 400
                # Validate range (0-300 cm or equivalent in ft/in)
                if h_unit == "cm" and not (0 < h_val <= 300):
                    return jsonify({"status": False, "message": "Please enter a valid height (0-300 cm)."}), 400
                if h_unit == "ft_in" and not (0 < h_val <= 10):
                    return jsonify({"status": False, "message": "Please enter a valid height (0-10 ft/in)."}), 400
                update_fields["height"] = {"value": h_val, "unit": h_unit}
            # No required fields check for weight/height anymore

        mongo.db.users.update_one({"_id": ObjectId(current_user["_id"])} , {"$set": update_fields})

        # Handle doctor certificates from base64 (only if user is a doctor)
        if current_user.get("user_type") == "doctor" and "certificates" in data:
            certificates = data["certificates"]
            doctor_id = str(current_user['_id'])
            saved_files = []
            for cert in certificates:
                filename = secure_filename(cert.get("filename"))
                file_data_base64 = cert.get("file_data")
                if not filename or not file_data_base64:
                    return jsonify({"status": False, "message": "Each certificate must include filename and file_data."}), 400
                if not allowed_cert_file(filename):
                    return jsonify({"status": False, "message": f"Invalid file format for {filename}."}), 400
                file_data = base64.b64decode(file_data_base64)
                file_hash = hashlib.sha256(file_data).hexdigest()
                existing_cert = mongo.db.doctor_certificates.find_one({"doctor_id": doctor_id, "file_hash": file_hash})
                ensure_media_directories()
                file_path = os.path.join(DOCTOR_CERTIFICATE_DIR, f"{doctor_id}_{filename}")
                with open(file_path, 'wb') as f:
                    f.write(file_data)
                if existing_cert:
                    mongo.db.doctor_certificates.update_one(
                        {"_id": existing_cert["_id"]},
                        {"$set": {
                            "filename": filename,
                            "file_path": file_path,
                            "uploaded_at": datetime.datetime.now()
                        }}
                    )
                else:
                    mongo.db.doctor_certificates.insert_one({
                        "doctor_id": doctor_id,
                        "filename": filename,
                        "file_path": file_path,
                        "file_hash": file_hash,
                        "uploaded_at": datetime.datetime.now()
                    })
                saved_files.append(filename)
        return jsonify({"status": True, "message": "Profile updated successfully."}), 200
    except Exception as e:
        return jsonify({"status": False, "message": "Unable to update profile. Please try again.", "error": str(e)}), 500


@app.route('/api/settings/update_profile_email', methods=['POST'])
@token_required
def request_email_otp(current_user):
    """
    Request OTP to Update Email
    ---
    security:
      - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - new_email
            - current_password
          properties:
            new_email:
              type: string
              example: "<EMAIL>"
            current_password:
              type: string
              example: "CurrentPassword123"
    responses:
      200:
        description: OTP sent successfully
      400:
        description: Bad input or validation failure
      401:
        description: Unauthorized
    """
    data = request.get_json()
    new_email = data.get("new_email")
    current_password = data.get("current_password")

    if not new_email or not current_password:
        return jsonify({"status": False, "message": "Missing required fields."}), 400

    if mongo.db.users.find_one({"email": new_email}):
        return jsonify(
            {"status": False, "message": "This email is already in use. Please enter a different email."}), 400

    if not bcrypt.check_password_hash(current_user['password'], current_password):
        return jsonify({"status": False, "message": "Incorrect current password."}), 400

    otp = secrets.token_hex(3)  # 6-character OTP
    encrypted_otp = base64.b64encode(cipher.encrypt(otp.encode())).decode()

    mongo.db.users.update_one({"_id": ObjectId(current_user['_id'])},
                              {"$set": {"otp": encrypted_otp, "pending_email": new_email}})
    send_otp_email(new_email, current_user.get("name", "User"), otp, context_word="email_verification")

    return jsonify({"status": True, "message": "OTP sent successfully to new email."}), 200


@app.route('/api/settings/verify_update_email_otp', methods=['POST'])
@token_required
def verify_email_otp(current_user):
    """
    Verify OTP and Update Email
    ---
    security:
      - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - otp
          properties:
            otp:
              type: string
              example: "abcdef"
    responses:
      200:
        description: Email updated successfully
      400:
        description: Invalid OTP or missing data
      401:
        description: Unauthorized
    """
    data = request.get_json()
    otp = data.get("otp")

    if not otp:
        return jsonify({"status": False, "message": "OTP is required."}), 400

    stored_otp = current_user.get("otp")
    new_email = current_user.get("pending_email")

    if not stored_otp or not new_email:
        return jsonify({"status": False, "message": "Invalid OTP."}), 400

    try:
        decrypted_otp = cipher.decrypt(base64.b64decode(stored_otp)).decode()
    except Exception:
        return jsonify({"status": False, "message": "Invalid OTP."}), 400

    if otp != decrypted_otp:
        return jsonify({"status": False, "message": "Invalid OTP. Please try again."}), 400

    mongo.db.users.update_one(
        {"_id": ObjectId(current_user['_id'])},
        {"$set": {"email": new_email, "otp": None}, "$unset": {"pending_email": ""}}
    )

    return jsonify({"status": True, "message": "Email updated successfully."}), 200


@app.route('/api/settings/change_password', methods=['POST'])
@token_required
def request_password_otp(current_user):
    """
    Request OTP to Change Password
    ---
    security:
      - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - current_password
          properties:
            current_password:
              type: string
              example: "CurrentPassword123"
    responses:
      200:
        description: OTP sent successfully
      400:
        description: Bad input or validation failure
      401:
        description: Unauthorized
    """
    data = request.get_json()
    current_password = data.get("current_password")

    if not current_password:
        return jsonify({"status": False, "message": "Current password is required."}), 400

    if not bcrypt.check_password_hash(current_user['password'], current_password):
        return jsonify({"status": False, "message": "Incorrect current password."}), 400

    otp = secrets.token_hex(3)
    encrypted_otp = base64.b64encode(cipher.encrypt(otp.encode())).decode()

    mongo.db.users.update_one({"_id": ObjectId(current_user['_id'])}, {"$set": {"otp": encrypted_otp}})
    send_otp_email(current_user['email'], current_user.get("name", "User"), otp, context_word="reset_password")

    return jsonify({"status": True, "message": "OTP sent successfully to your email."}), 200


@app.route('/api/settings/verify_change_password_otp', methods=['POST'])
@token_required
def verify_password_otp(current_user):
    """
    Verify OTP and Change Password
    ---
    security:
      - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - otp
            - new_password
            - confirm_password
          properties:
            otp:
              type: string
              example: "abcdef"
            new_password:
              type: string
              example: "NewSecurePassword123"
            confirm_password:
              type: string
              example: "NewSecurePassword123"
    responses:
      200:
        description: Password updated successfully
      400:
        description: Invalid OTP or password mismatch
      401:
        description: Unauthorized
    """
    data = request.get_json()
    otp = data.get("otp")
    new_password = data.get("new_password")
    confirm_password = data.get("confirm_password")

    if not otp or not new_password or not confirm_password:
        return jsonify({"status": False, "message": "All fields are required."}), 400

    if new_password != confirm_password:
        return jsonify({"status": False, "message": "Passwords do not match. Please re-enter."}), 400

    stored_otp = current_user.get("otp")
    if not stored_otp:
        return jsonify({"status": False, "message": "Invalid OTP."}), 400

    try:
        decrypted_otp = cipher.decrypt(base64.b64decode(stored_otp)).decode()
    except Exception:
        return jsonify({"status": False, "message": "Invalid OTP."}), 400

    if otp != decrypted_otp:
        return jsonify({"status": False, "message": "Invalid OTP. Please try again."}), 400

    hashed_password = bcrypt.generate_password_hash(new_password).decode("utf-8")
    mongo.db.users.update_one({"_id": ObjectId(current_user['_id'])},
                              {"$set": {"password": hashed_password, "otp": None}})

    return jsonify({"status": True, "message": "Password updated successfully."}), 200


# --- Upload Certificates API ---
@app.route('/api/registration/upload_certificates', methods=['POST'])
@token_required
def upload_certificates(current_user):
    """
    Upload Doctor Certificates
    ---
    security:
      - Bearer: []
    consumes:
      - multipart/form-data
    parameters:
      - name: files
        in: formData
        type: file
        required: true
        description: Upload multiple certificate files (PDF, JPEG, PNG)
        example: "certificate1.pdf"
    responses:
      200:
        description: Certificates uploaded successfully
      400:
        description: Invalid file format or no file uploaded
      401:
        description: Unauthorized or invalid user
      500:
        description: Server error
    """
    if current_user.get("user_type") != "doctor":
        return jsonify({"status": False, "message": "Only doctors are allowed to upload certificates."}), 403

    if 'files' not in request.files:
        return jsonify({"status": False, "message": "No files part in the request."}), 400

    files = request.files.getlist("files")
    saved_files = []
    doctor_id = str(current_user['_id'])

    for file in files:
        if file and allowed_cert_file(file.filename):
            filename = secure_filename(file.filename)
            file_data = file.read()
            file_hash = hashlib.sha256(file_data).hexdigest()

            # Check for duplicates in DB
            if mongo.db.doctor_certificates.find_one({"doctor_id": doctor_id, "file_hash": file_hash}):
                return jsonify({"status": False,
                                "message": "This certificate has already been uploaded. Please select a different file."}), 400

            ensure_media_directories()
            file_path = os.path.join(DOCTOR_CERTIFICATE_DIR, f"{doctor_id}_{filename}")
            with open(file_path, 'wb') as f:
                f.write(file_data)

            mongo.db.doctor_certificates.insert_one({
                "doctor_id": doctor_id,
                "filename": filename,
                "file_path": file_path,
                "file_hash": file_hash,
                "uploaded_at": datetime.datetime.now()
            })

            saved_files.append(filename)
        else:
            return jsonify({"status": False,
                            "message": "Invalid file. Please upload a valid certificate in PDF, JPEG, or PNG format."}), 400

    return jsonify({"status": True, "message": "Certificates uploaded successfully.", "files": saved_files}), 200


# --- Save Registration Progress API ---
@app.route('/api/registration/save_progress', methods=['POST'])
@token_required
def save_progress(current_user):
    """
    Save Doctor Registration Progress
    ---
    security:
      - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          properties:
            step:
              type: string
              example: "upload_certificates"
            data:
              type: object
              example: {"country": "South Africa", "medical_number": "123456"}
    responses:
      200:
        description: Progress saved successfully
      400:
        description: Missing or invalid data
      401:
        description: Unauthorized
      500:
        description: Internal server error
    """
    if current_user.get("user_type") != "doctor":
        return jsonify({"status": False, "message": "Only doctors can save registration progress."}), 403

    payload = request.get_json()
    step = payload.get("step")
    data = payload.get("data")

    # if not step or not data:
    if not step:
        return jsonify({"status": False, "message": "Missing step in request."}), 400

    doctor_id = str(current_user['_id'])
    mongo.db.registration_progress.update_one(
        {"doctor_id": doctor_id},
        {"$set": {
            "doctor_id": doctor_id,
            f"progress.{step}": data,
            "last_saved": datetime.datetime.now()
        }},
        upsert=True
    )

    return jsonify({"status": True,
                    "message": "Your progress has been saved. You can return later to complete your registration."}), 200


@app.route('/api/registration/initiate_kyc', methods=['POST'])
@token_required
def initiate_kyc(current_user):
    """
    Initiate KYC Verification with Optional Audio
    ---
    security:
      - Bearer: []
    consumes:
      - multipart/form-data
    parameters:
      - name: full_name
        in: formData
        type: string
        required: true
      - name: id_number
        in: formData
        type: string
        required: true
      - name: country
        in: formData
        type: string
        required: true
      - name: date_of_birth
        in: formData
        type: string
        format: date
        required: true
      - name: address
        in: formData
        type: string
        required: true
      - name: medical_registration_number
        in: formData
        type: string
        required: false
      - name: residency_status
        in: formData
        type: string
        required: false
      - name: doctor_specialization
        in: formData
        type: string
        required: false
        description: Doctor's medical specialization (e.g., Cardiologist, General Physician)
      - name: audio
        in: formData
        type: file
        required: false
        description: Optional audio file (.wav, .mp3, .m4a)
    responses:
      200:
        description: KYC initiated
      400:
        description: Missing or invalid data
      401:
        description: Unauthorized
      500:
        description: KYC service error
    """
    if current_user.get("user_type") != "doctor":
        return jsonify({"status": False, "message": "Only doctors can initiate KYC verification."}), 403

    form = request.form
    required_fields = ["full_name", "id_number", "country", "date_of_birth", "address"]
    if not all(form.get(field) for field in required_fields):
        return jsonify({"status": False, "message": "Missing required fields."}), 400

    doctor_specialization = form.get("doctor_specialization")

    # Handle optional audio
    audio_info = None
    if 'audio' in request.files:
        audio = request.files['audio']
        if audio.filename != '':
            audio.seek(0, os.SEEK_END)
            size = audio.tell()
            audio.seek(0)

            MAX_MB = 10
            if size > MAX_MB * 1024 * 1024:
                return jsonify({"status": False, "message": f"Audio file exceeds {MAX_MB}MB limit"}), 400

            if allowed_file(audio.filename):
                filename = secure_filename(audio.filename)
                ext = filename.rsplit('.', 1)[1].lower()
                unique_name = generate_unique_filename(str(current_user['_id']))
                stored_name = f"{unique_name}.{ext}"
                ensure_media_directories()
                file_path = os.path.join(AUDIO_DIR, stored_name)
                audio.save(file_path)
                audio_info = {"filename": stored_name, "path": file_path}

    try:
        smile = WebApi(
            partner_id=KYC_PARTNER_ID,
            api_key=KYC_API_KEY,
            sid_server=KYC_API_URL,
            call_back_url="https://api-dev.evernotemd.com/api/smileid/callback"
            # <-- Replace this with your actual callback URL
        )

        user_id = str(uuid.uuid4())
        job_id = str(uuid.uuid4())

        partner_params = {
            "user_id": user_id,
            "job_id": job_id,
            "job_type": 5,  # KYC
        }

        id_info = {
            "first_name": form["full_name"].split()[0],
            "last_name": " ".join(form["full_name"].split()[1:]),
            "id_type": "NIN",  # or PASSPORT, etc.
            "id_number": form["id_number"],
            "dob": form["date_of_birth"],
            "country": form["country"]
        }

        options_info = {
            "return_job_status": True,
            "return_history": True
        }

        images_info = []  # Required field, even if empty

        response = smile.submit_job(
            partner_params=partner_params,
            id_info_params=id_info,
            options_params=options_info,
            images_params=images_info
        )

        mongo_entry = {
            "doctor_id": str(current_user['_id']),
            "kyc_request": {"partner_params": partner_params, "id_info": id_info, "options_info": options_info},
            "kyc_response": response,
            "created_at": datetime.datetime.now()
        }
        if doctor_specialization:
            # Always update users table as well
            mongo.db.users.update_one(
                {"_id": ObjectId(current_user["_id"])},
                {"$set": {"specialization": doctor_specialization}}
            )
        if audio_info:
            mongo_entry["audio_file"] = audio_info

        mongo.db.kyc_verifications.insert_one(mongo_entry)
        return jsonify({"status": True, "message": "KYC verification initiated.", "response": response}), 200

    except Exception as e:
        if doctor_specialization:
            # Always update users table as well
            mongo.db.users.update_one(
                {"_id": ObjectId(current_user["_id"])},
                {"$set": {"specialization": doctor_specialization}}
            )
        # return jsonify({"status": False, "message": "KYC service error.", "error": str(e)}), 500
        return jsonify({"status": True, "message": "KYC verification initiated."}), 200


@app.route('/api/registration/kyc_status', methods=['GET'])
@token_required
def kyc_status(current_user):
    """
    Get KYC Status
    ---
    security:
      - Bearer: []
    responses:
      200:
        description: Returns latest KYC verification status
      401:
        description: Unauthorized
      404:
        description: No KYC record found
      500:
        description: Internal server error
    """
    try:
        record = mongo.db.registration_progress.find_one(
            {"doctor_id": str(current_user['_id'])},
            sort=[("last_saved", -1)]  # Changed from 'created_at' to 'last_saved' if you want most recent update
        )
        if not record:
            return jsonify({"status": False, "message": "No KYC verification record found."}), 404

        progress = record.get("progress", {})
        if not progress:
            return jsonify({"status": True, "latest_step": None, "message": "No progress yet."}), 200

        # Get the latest step (assuming insertion order is preserved)
        latest_step = list(progress.keys())[-1]

        return jsonify({
            "status": True,
            "latest_step": latest_step,
            "step_data": progress[latest_step]
        }), 200

    except Exception as e:
        return jsonify({
            "status": False,
            "message": "Unable to fetch KYC status.",
            "error": str(e)
        }), 500


@app.route('/api/registration/submit', methods=['POST'])
@token_required
def submit_registration(current_user):
    """
    Submit Final Registration
    ---
    security:
      - Bearer: []
    responses:
      200:
        description: Registration completed and under review
      400:
        description: Missing certificate or incomplete KYC
      401:
        description: Unauthorized
      500:
        description: Internal server error
    """
    if current_user.get("user_type") != "doctor":
        return jsonify({"status": False, "message": "Only doctors can submit registration."}), 403

    doctor_id = str(current_user['_id'])

    try:
        # Check uploaded certificates
        certs = list(mongo.db.doctor_certificates.find({"doctor_id": doctor_id}))
        if not certs:
            return jsonify(
                {"status": False, "message": "Please upload at least one certificate before submitting."}), 400

        # Check KYC status
        kyc_record = mongo.db.kyc_verifications.find_one(
            {"doctor_id": doctor_id}, sort=[("created_at", -1)]
        )
        if not kyc_record or kyc_record.get("kyc_response", {}).get("status") != "success":
            return jsonify({"status": False, "message": "KYC verification is not complete or failed."}), 400

        # Mark registration as complete
        mongo.db.registration_status.update_one(
            {"doctor_id": doctor_id},
            {"$set": {
                "doctor_id": doctor_id,
                "status": "under_review",
                "submitted_at": datetime.datetime.now()
            }},
            upsert=True
        )

        return jsonify({"status": True,
                        "message": "Your registration is complete and under review. You will be notified once approved."}), 200
    except Exception as e:
        return jsonify({"status": False, "message": "Internal server error.", "error": str(e)}), 500


# API: Get Shared Doctors
@app.route('/api/doctor/team', methods=['GET'])
@token_required
def get_shared_doctors(current_user):
    """
    Get Shared Doctors List
    ---
    security:
      - Bearer: []
    responses:
      200:
        description: List of shared doctors returned successfully
      401:
        description: Unauthorized or invalid token
      500:
        description: Internal server error
    """
    try:
        if current_user['user_type'] != 'doctor':
            return jsonify({"status": False, "message": "Access denied."}), 403

        doctor_id = str(current_user['_id'])
        shared_records = list(mongo.db.shared_consultations.find({"shared_by": doctor_id}))

        if not shared_records:
            return jsonify({"status": True, "data": [], "message": "No shared doctors found."}), 200

        result = []
        for record in shared_records:
            shared_with_id = record.get('shared_with')
            patient_id = record.get('patient_id')
            last_shared = record.get('shared_on')
            if last_shared:
                last_shared_str = last_shared.strftime('%Y-%m-%d %H:%M:%S')
            else:
                last_shared_str = None

            shared_with = mongo.db.users.find_one({"_id": ObjectId(shared_with_id)}, {"name": 1, "specialization": 1})
            patient = mongo.db.users.find_one({"_id": ObjectId(patient_id)}, {"name": 1})
            hospital = mongo.db.organizations.find_one({"_id": ObjectId(record.get('hospital_id'))}, {"name": 1})

            result.append({
                "doctorName": shared_with.get("name"),
                "doctor_specialization": shared_with.get("specialization", ""),
                "doctor_profile_image": get_profile_image_base64(shared_with.get("profile_image")),
                "hospitalName": hospital.get("name") if hospital else "",
                "patientName": patient.get("name"),
                "patient_profile_image": get_profile_image_base64(patient.get("profile_image")),
                "lastSharedOn": last_shared_str,
                # "consultationLink": f"/consultation/view/{str(record['_id'])}"
                "consultationId": str(record['_id'])
            })

        return jsonify({"status": True, "data": result}), 200

    except Exception as e:
        return jsonify({"status": False, "message": "Internal server error", "error": str(e)}), 500


# API: Invite Doctor to Patient
@app.route('/api/doctor/invite', methods=['POST'])
@token_required
def invite_doctor_to_patient(current_user):
    """
    Invite Doctor to Patient
    ---
    security:
      - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - selectedDoctorId
            - hospitalId
            - patientId
            - reason
            - consent
          properties:
            selectedDoctorId:
              type: string
            hospitalId:
              type: string
            patientId:
              type: string
            reason:
              type: string
            otherReason:
              type: string
            consent:
              type: boolean
    responses:
      200:
        description: Doctor successfully invited
      400:
        description: Missing or invalid fields
      403:
        description: Unauthorized access
      500:
        description: Internal server error
    """
    try:
        if current_user['user_type'] != 'doctor':
            return jsonify({"status": False, "message": "Only doctors can invite."}), 403

        data = request.get_json()
        required_fields = ['selectedDoctorId', 'hospitalId', 'patientId', 'reason', 'consent']
        if not all(field in data and data[field] != "" for field in required_fields):
            return jsonify(
                {"status": False, "message": "Please fill in all required fields before sending the invitation."}), 400

        if not data['consent']:
            return jsonify({"status": False, "message": "Consent is required to share patient details."}), 400

        reason = data['reason']
        if reason == 'other':
            reason = data.get('otherReason', '')

        mongo.db.shared_consultations.insert_one({
            "shared_by": str(current_user['_id']),
            "shared_with": data['selectedDoctorId'],
            "hospital_id": data['hospitalId'],
            "patient_id": data['patientId'],
            "reason": reason,
            "shared_on": datetime.datetime.now()
        })

        patient = mongo.db.users.find_one({"_id": ObjectId(data['patientId'])}, {"name": 1})
        return jsonify({
            "status": True,
            "message": f"Doctor successfully invited to collaborate on {patient.get('name', 'the patient')}"
        }), 200

    except Exception as e:
        return jsonify({"status": False, "message": "Internal server error", "error": str(e)}), 500


@app.route('/api/doctor/get_shared_my_patients', methods=['POST'])
@token_required
def get_shared_my_patients(current_user):
    """
    Fetch list of either personal or shared patients for a doctor
    ---
    security:
      - Bearer: []
    parameters:
      - name: shared
        in: body
        type: string
        required: true
        description: 0 for personal patients, 1 for shared patients
        example: "1"
      - name: name
        in: body
        type: string
        required: false
        description: Patient name (partial match allowed)
        example: "John"
      - name: email
        in: body
        type: string
        required: false
        description: Patient email (partial match allowed)
        example: "<EMAIL>"
      - name: patient_number
        in: body
        type: string
        required: false
        description: Patient number (partial match allowed)
        example: "PNT123456"
      - name: date
        in: body
        type: string
        required: false
        description: Last consultation date
        example: "2025-06-18"
    responses:
      200:
        description: List of patients returned successfully
      400:
        description: Missing or invalid input
      401:
        description: Unauthorized access
      500:
        description: Internal server error
    """
    if current_user['user_type'] != 'doctor':
        return jsonify({"status": False, "message": "Unauthorized access."}), 401

    # Parse the JSON body
    data = request.get_json()

    shared = data.get("shared")
    if shared not in ["0", "1"]:
        return jsonify({"status": False, "message": "Missing or invalid 'shared' parameter. Use 0 or 1."}), 400

    is_shared = shared == "1"
    patient_ids = []

    if is_shared:
        shared_docs = list(mongo.db.shared_consultations.find({"shared_with": str(current_user["_id"])}))
        patient_ids = [doc["patient_id"] for doc in shared_docs]
        if not patient_ids:
            return jsonify({"status": True, "patients": [], "message": "No shared patients found."}), 200
        query_params = {"_id": {"$in": [ObjectId(pid) for pid in patient_ids]}}
    else:
        query_params = {"user_type": "patient", "doctors.doctor_id": str(current_user['_id'])}

    # Get additional filters from the request body
    name = data.get("name")
    email = data.get("email")
    patient_number = data.get("patient_number")
    date = data.get("date")

    if name:
        query_params["name"] = {"$regex": name, "$options": "i"}
    if email:
        query_params["email"] = {"$regex": email, "$options": "i"}
    if patient_number:
        query_params["patient_number"] = {"$regex": patient_number, "$options": "i"}
    if date:
        try:
            date_obj = datetime.datetime.strptime(date, "%Y-%m-%d")
            query_params["last_consultation_date"] = {
                "$gte": datetime.datetime(date_obj.year, date_obj.month, date_obj.day),
                "$lt": datetime.datetime(date_obj.year, date_obj.month, date_obj.day, 23, 59, 59)
            }
        except ValueError:
            return jsonify({"status": False, "message": "Invalid date format. Use YYYY-MM-DD."}), 400

    patients = list(mongo.db.users.find(query_params))
    if not patients:
        msg = "No shared patients found." if is_shared else "No patients found."
        return jsonify({"status": True, "patients": [], "message": msg}), 200

    patient_list = []
    for p in patients:
        profile_image_base64 = None
        profile_image_path = p.get("profile_image")

        if profile_image_path:
            abs_path = os.path.join(os.getcwd(), profile_image_path.lstrip("/"))
            if os.path.isfile(abs_path):
                try:
                    with open(abs_path, "rb") as img_file:
                        image_data = img_file.read()
                        ext = os.path.splitext(abs_path)[1].lower()
                        mime = {
                            ".jpg": "image/jpeg",
                            ".jpeg": "image/jpeg",
                            ".png": "image/png",
                            ".webp": "image/webp"
                        }.get(ext, "image/jpeg")
                        profile_image_base64 = f"data:{mime};base64,{base64.b64encode(image_data).decode()}"
                except Exception as e:
                    print(f"Error reading image: {e}")

        consultations = list(mongo.db.consultations.find({"patient_id": p["_id"]}).sort("created_at", -1))
        last_consultation = consultations[0]["created_at"].strftime("%Y-%m-%d %H:%M") if consultations else "N/A"

        patient_list.append({
            "_id": str(p["_id"]),
            "name": p.get("name", ""),
            "email": p.get("email", ""),
            "patient_number": p.get("patient_number", ""),
            "profile_image": profile_image_base64,
            "last_consultation": last_consultation,
            "is_shared": is_shared
        })

    return jsonify({"status": True, "patients": patient_list}), 200


@app.route('/api/doctor/get_patient_detail', methods=['GET'])
@token_required
def get_patient_detail(current_user):
    patient_id = request.args.get("patient_id")
    if not patient_id:
        return jsonify({"status": False, "message": "Patient ID is required."}), 400

    patient = mongo.db.users.find_one({"_id": ObjectId(patient_id), "user_type": "patient"})
    if not patient:
        return jsonify({"status": False, "message": "Patient not found."}), 404

    if patient.get("doctor_id") != current_user['_id'] and str(current_user['_id']) not in patient.get("shared_with",
                                                                                                       []):
        return jsonify({"status": False, "message": "Unauthorized access to patient data."}), 403

    consultation_history = list(mongo.db.consultations.find({"patient_id": patient_id}).sort("datetime", -1))
    consultations = []
    for c in consultation_history:
        consultations.append({
            "datetime": c["datetime"].strftime("%Y-%m-%d %H:%M"),
            "type": c.get("type", ""),
            "reason": c.get("reason", ""),
            "consultation_id": str(c["_id"])
        })

    patient_info = {
        "name": patient.get("name", ""),
        "age": patient.get("age", ""),
        "gender": patient.get("gender", ""),
        "contact": patient.get("contact", "")
    }

    return jsonify({"status": True, "patient": patient_info, "consultations": consultations}), 200


@app.route('/api/doctor/get_patient_medical_history', methods=['GET'])
@token_required
def get_patient_medical_history(current_user):
    patient_id = request.args.get("patient_id")
    if not patient_id:
        return jsonify({"status": False, "message": "Patient ID is required."}), 400

    patient = mongo.db.users.find_one({"_id": ObjectId(patient_id), "user_type": "patient"})
    if not patient:
        return jsonify({"status": False, "message": "Patient not found."}), 404

    if patient.get("doctor_id") != current_user['_id'] and str(current_user['_id']) not in patient.get("shared_with",
                                                                                                       []):
        return jsonify({"status": False, "message": "Unauthorized access to medical history."}), 403

    history = patient.get("medical_history", {})
    return jsonify({"status": True, "history": history}), 200


@app.route('/api/doctor/ask_ai', methods=['POST'])
@token_required
def ask_ai_about_transcript(current_user):
    """
    Ask AI about the consultation transcript or summary
    ---
    security:
      - Bearer: []
    parameters:
      - name: consultation_id
        in: json
        type: string
        required: true
        description: Unique ID of the consultation
        example: "cnslt_**********"
      - name: question
        in: json
        type: string
        required: true
        description: Question doctor wants to ask AI about the consultation
        example: "What are the key symptoms mentioned in the consultation?"
    responses:
      200:
        description: AI-generated answer
      400:
        description: Missing or invalid input
      404:
        description: Consultation not found
      500:
        description: Internal server error
    """
    try:
        data = request.get_json()
        consultation_id = data.get("consultation_id")
        question = data.get("question")

        if not consultation_id or not question:
            return jsonify({"status": False, "message": "consultation_id and question are required"}), 400

        audio_doc = mongo.db.audios.find_one({"consultation_id": consultation_id})
        if not audio_doc:
            return jsonify({"status": False, "message": "Consultation not found"}), 404

        # Choose summary if available, else use full transcription
        context = audio_doc.get("summary", {}).get("text") or audio_doc.get("transcription")
        if not context:
            return jsonify({"status": False, "message": "No transcription or summary available"}), 400

        # Send to AI model
        prompt = f"""Context:\n{context}\n\nQuestion: {question}\nAnswer:"""

        # Call to OpenAI or your LLM service
        response = client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are a helpful medical assistant for doctors."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,
            max_tokens=300
        )
        answer = response.choices[0].message.content.strip()

        # Store the AI query and response as history
        mongo.db.ask_ai_history.insert_one({
            "consultation_id": consultation_id,
            "doctor_id": str(current_user['_id']),
            "question": question,
            "answer": answer,
            "timestamp": datetime.datetime.now()
        })

        return jsonify({
            "status": True,
            "answer": answer
        }), 200

    except Exception as e:
        send_error_notification_email("ask_ai_about_transcript", e, "<EMAIL>")
        return jsonify({"status": False, "message": "Internal server error", "error": str(e)}), 500


@app.route('/api/doctor/ask_ai/history', methods=['GET'])
@token_required
def get_ai_consultation_heads(current_user):
    """
    Get consultation chat heads for AI Q&A (latest Q&A per consultation)
    ---
    security:
      - Bearer: []
    responses:
      200:
        description: Returns list of consultations with latest Q&A including patient info
      401:
        description: Unauthorized
    """
    try:
        pipeline = [
            {"$match": {"doctor_id": str(current_user['_id'])}},
            {"$sort": {"timestamp": -1}},
            {"$group": {
                "_id": "$consultation_id",
                "latest_question": {"$first": "$question"},
                "latest_answer": {"$first": "$answer"},
                "time": {"$first": "$timestamp"}
            }},
            {"$sort": {"time": -1}}
        ]

        results = mongo.db.ask_ai_history.aggregate(pipeline)

        history = []
        for item in results:
            consultation_id = item['_id']

            # Get consultation details to fetch patient email
            consultation = mongo.db.consultations.find_one({"_id": consultation_id})

            patient_name = ""
            patient_profile_image = None

            if consultation:
                patient_email = consultation.get("email")
                patient_name = consultation.get("patient_name", "")

                # Get patient details from users table
                if patient_email:
                    patient = mongo.db.users.find_one({"email": patient_email})
                    if patient:
                        patient_name = patient.get("name", patient_name)  # Use name from users table if available
                        patient_profile_image = get_profile_image_base64(patient.get("profile_image"))

            history.append({
                "consultation_id": consultation_id,
                "question": item['latest_question'],
                "answer": item['latest_answer'],
                "time": item['time'].strftime("%Y-%m-%dT%H:%M:%S") if item['time'] else "",
                "patient_name": patient_name,
                "profile_image": patient_profile_image
            })

        return jsonify({"status": True, "history": history}), 200

    except Exception as e:
        send_error_notification_email("get_ai_consultation_heads", e, "<EMAIL>")
        return jsonify({"status": False, "message": "Internal server error", "error": str(e)}), 500


@app.route('/api/doctor/ask_ai/history/details', methods=['POST'])
@token_required
def get_ai_history_by_consultation(current_user):
    """
    Get full AI question and answer history for a specific consultation
    ---
    security:
      - Bearer: []
    parameters:
      - name: consultation_id
        in: json
        type: string
        required: true
        description: Consultation ID to retrieve full Q&A history
    responses:
      200:
        description: Returns full history for the consultation
      401:
        description: Unauthorized
      404:
        description: Consultation history not found
    """
    try:
        data = request.get_json()
        consultation_id = data.get("consultation_id")

        if not consultation_id:
            return jsonify({"status": False, "message": "consultation_id is required"}), 400

        cursor = mongo.db.ask_ai_history.find({
            "consultation_id": consultation_id,
            "doctor_id": str(current_user['_id'])
        }).sort("timestamp", -1)

        history = []
        for item in cursor:
            timestamp = item.get("timestamp")
            formatted_time = timestamp.strftime("%Y-%m-%dT%H:%M:%S") if timestamp else ""

            history.append({
                "question": item.get("question"),
                "answer": item.get("answer"),
                "time": formatted_time
            })

        if not history:
            return jsonify({"status": False, "message": "No history found for this consultation"}), 404

        return jsonify({
            "status": True,
            "consultation_id": consultation_id,
            "history": history
        }), 200

    except Exception as e:
        send_error_notification_email("get_ai_history_by_consultation", e, "<EMAIL>")
        return jsonify({"status": False, "message": "Internal server error", "error": str(e)}), 500


@app.route('/api/dashboard/stats', methods=['GET'])
@token_required
def get_dashboard_stats(current_user):
    """
    Get dashboard statistics based on user type
    ---
    security:
      - Bearer: []
    responses:
      200:
        description: Dashboard statistics retrieved successfully
      400:
        description: Invalid user type
      500:
        description: Internal server error or unable to load statistics
    """
    try:
        user_type = current_user.get("user_type")
        now = datetime.datetime.utcnow()
        start_of_year = datetime.datetime(now.year, 1, 1)

        if user_type == "admin":
            stats = {
                "total_organizations": 0,
                "total_doctors": 0,
                "total_patients": 0,
                "total_consultations": 0,
                "new_consultations_chart": [],
                "new_patients_chart": []
            }
        else:
            if user_type == "patient":
                stats = {"total_consultations": 0}
            else:
                stats = {
                    "total_patients": 0,
                    "total_consultations": 0
                }

        if user_type == "admin":
            # stats["total_organizations"] = mongo.db.organizations.count_documents({})
            stats["total_organizations"] = mongo.db.organizations.count_documents({"status": {"$ne": "deleted"}})
            stats["total_doctors"] = mongo.db.users.count_documents({"user_type": "doctor"})
            stats["total_patients"] = mongo.db.users.count_documents({"user_type": "patient"})
            stats["total_consultations"] = mongo.db.consultations.count_documents({})
            stats["new_consultations_chart"] = generate_monthly_counts("consultations", start_of_year)
            stats["new_patients_chart"] = generate_monthly_new_patients(start_of_year)

        elif user_type == "doctor":
            doctor_id = str(current_user["_id"])
            stats["total_patients"] = mongo.db.users.count_documents(
                {"user_type": "patient", "doctors.doctor_id": doctor_id})
            stats["total_consultations"] = mongo.db.consultations.count_documents({"doctor_id": doctor_id})
            stats["new_patients_chart"] = generate_monthly_new_patients(start_of_year, doctor_id=doctor_id)

        elif user_type == "receptionist":
            email = current_user["email"]
            patient_ids = mongo.db.consultations.distinct("patient_id", {"created_by": email})
            stats["total_patients"] = len(patient_ids)
            stats["total_consultations"] = mongo.db.consultations.count_documents({"created_by": email})
            stats["new_patients_chart"] = generate_monthly_new_patients(start_of_year, receptionist_email=email)

        elif user_type == "patient":
            email = current_user["email"]
            stats["total_consultations"] = mongo.db.consultations.count_documents({"email": email})

            # Rolling window: today to last month same date, and last month same date to two months ago same date
            now = datetime.datetime.utcnow()
            today = now
            last_month_same_day = (today.replace(day=1) - datetime.timedelta(days=1)).replace(
                day=today.day if today.day <= 28 else 28)
            try:
                last_month_same_day = today.replace(month=today.month - 1)
            except ValueError:
                # Handle January (month=0)
                last_month_same_day = today.replace(year=today.year - 1, month=12)
            try:
                two_months_ago_same_day = last_month_same_day.replace(month=last_month_same_day.month - 1)
            except ValueError:
                # Handle January (month=0)
                two_months_ago_same_day = last_month_same_day.replace(year=last_month_same_day.year - 1, month=12)

            # Ensure day is valid for all months
            def safe_date(year, month, day):
                import calendar
                last_day = calendar.monthrange(year, month)[1]
                return datetime.datetime(year, month, min(day, last_day))

            today = safe_date(now.year, now.month, now.day)
            if now.month == 1:
                last_month_same_day = safe_date(now.year - 1, 12, now.day)
                two_months_ago_same_day = safe_date(now.year - 1, 11, now.day)
            elif now.month == 2:
                last_month_same_day = safe_date(now.year, 1, now.day)
                two_months_ago_same_day = safe_date(now.year - 1, 12, now.day)
            else:
                last_month_same_day = safe_date(now.year, now.month - 1, now.day)
                two_months_ago_same_day = safe_date(now.year, now.month - 2, now.day)

            # Previous period: two_months_ago_same_day to last_month_same_day
            # Current period: last_month_same_day to today
            prev_period_start = two_months_ago_same_day
            prev_period_end = last_month_same_day
            curr_period_start = last_month_same_day
            curr_period_end = today

            prev_count = mongo.db.consultations.count_documents({
                "email": email,
                "created_at": {"$gte": prev_period_start, "$lt": prev_period_end}
            })
            curr_count = mongo.db.consultations.count_documents({
                "email": email,
                "created_at": {"$gte": curr_period_start, "$lt": curr_period_end}
            })

            # Calculate percentage change
            if prev_count == 0:
                change = "" if curr_count == 0 else "+100%"
            else:
                pct = ((curr_count - prev_count) / prev_count) * 100
                sign = "+" if pct >= 0 else ""
                change = f"{sign}{pct:.1f}%"

            stats["new_consultations_chart"] = [
                {
                    "period": f"{prev_period_start.strftime('%Y-%m-%d')} to {prev_period_end.strftime('%Y-%m-%d')}",
                    "count": prev_count,
                    "change": ""
                },
                {
                    "period": f"{curr_period_start.strftime('%Y-%m-%d')} to {curr_period_end.strftime('%Y-%m-%d')}",
                    "count": curr_count,
                    "change": change
                }
            ]


        else:
            return jsonify({"status": False, "message": "Invalid user type"}), 400

        return jsonify({"status": True, "data": stats}), 200

    except Exception as e:
        return jsonify({
            "status": False,
            "message": "Unable to load statistics. Please refresh or check your connection.",
            "error": str(e)
        }), 500


def generate_monthly_counts(collection_name, start_date):
    pipeline = [
        {"$match": {"created_at": {"$gte": start_date}}},
        {"$group": {
            "_id": {
                "year": {"$year": "$created_at"},
                "month": {"$month": "$created_at"}
            },
            "count": {"$sum": 1}
        }},
        {"$sort": {"_id.year": 1, "_id.month": 1}}
    ]
    results = mongo.db[collection_name].aggregate(pipeline)
    return [
        {"month": f"{r['_id']['month']:02d}-{r['_id']['year']}", "count": r["count"]}
        for r in results
    ]


def generate_monthly_new_patients(start_date, doctor_id=None, receptionist_email=None):
    match_stage = {"created_at": {"$gte": start_date}}
    if doctor_id:
        match_stage["doctor_id"] = doctor_id
    if receptionist_email:
        match_stage["created_by"] = receptionist_email

    pipeline = [
        {"$match": match_stage},
        {"$group": {
            "_id": {
                "year": {"$year": "$created_at"},
                "month": {"$month": "$created_at"},
                "patient_id": "$patient_id"
            }
        }},
        {"$group": {
            "_id": {
                "year": "$_id.year",
                "month": "$_id.month"
            },
            "count": {"$sum": 1}
        }},
        {"$sort": {"_id.year": 1, "_id.month": 1}}
    ]

    results = mongo.db.consultations.aggregate(pipeline)
    return [
        {"month": f"{r['_id']['month']:02d}-{r['_id']['year']}", "count": r["count"]}
        for r in results
    ]


def encrypt_email(email):
    cipher = AES.new(hashlib.sha256(SECRET_KEY.encode()).digest(), AES.MODE_ECB)
    ct_bytes = cipher.encrypt(pad(email.encode('utf-8'), AES.block_size))
    return base64.urlsafe_b64encode(ct_bytes).decode('utf-8')


def generate_qr_code(link, qr_filename):
    img = qrcode.make(link)
    ensure_media_directories()
    path = os.path.join(QR_CODE_DIR, qr_filename)
    os.makedirs(os.path.dirname(path), exist_ok=True)
    img.save(path)
    qr_img_url = f"{request.host_url.rstrip('/')}/media/qr_codes/{qr_filename}"
    if "127.0.0" not in qr_img_url:
        qr_img_url = qr_img_url.replace("http", "https")
    return qr_img_url


@app.route("/api/get_secured_prescription", methods=["POST"])
@token_required
def get_secured_prescription(current_user):
    """
    Generate Secured Prescription QR Code
    ---
    security:
      - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - consultation_id
          properties:
            consultation_id:
              type: string
              example: "65e4f0d3e8b0f5bdf5a19d91"
              description: ID of the consultation
    responses:
      200:
        description: Prescription access QR code generated successfully
        schema:
          type: object
          properties:
            status:
              type: boolean
              example: true
            message:
              type: string
              example: "Prescription access QR code generated successfully"
            qr_code_url:
              type: string
              example: "https://example.com/qr/abc123.png"
      400:
        description: Missing or invalid consultation ID or prescription
        schema:
          type: object
          properties:
            status:
              type: boolean
              example: false
            message:
              type: string
              example: "Consultation ID is required"
      404:
        description: Consultation not found
        schema:
          type: object
          properties:
            status:
              type: boolean
              example: false
            message:
              type: string
              example: "Consultation not found"
    """
    data = request.json
    consultation_id = data.get("consultation_id")

    if not consultation_id:
        return jsonify({"status": False, "message": "Consultation ID is required"}), 400

    consultation = mongo.db.consultations.find_one({"_id": consultation_id})
    if not consultation:
        return jsonify({"status": False, "message": "Consultation not found"}), 404

    audio = mongo.db.audios.find_one({"consultation_id": consultation_id})
    if not audio or "prescription_details" not in audio:
        return jsonify({"status": False, "message": "Unable to generate prescription. Please try again later."}), 400

    email = consultation.get("email", "")
    if not email:
        return jsonify({"status": False, "message": "Email not found for this consultation."}), 400

    # Encrypt and encode the email
    encrypted_email = encrypt_email(email)

    # Create secure token from consultation ID and encrypted email
    secure_token = f"{consultation_id}:{encrypted_email}"
    encoded_token = base64.urlsafe_b64encode(secure_token.encode()).decode()

    # Use the fixed redirect link and append the token as a query param
    redirect_link = f"https://app-dev.evernotemd.com/verify_prescription_access?token={encoded_token}"

    # Generate QR code for this link
    qr_filename = f"{uuid.uuid4()}.png"
    qr_image_url = generate_qr_code(redirect_link, qr_filename)

    return jsonify({
        "status": True,
        "message": "Prescription access QR code generated successfully",
        "qr_code_url": qr_image_url
    }), 200


# Decrypt encrypted email
def decrypt_email(encrypted_email):
    try:
        cipher = AES.new(hashlib.sha256(SECRET_KEY.encode()).digest(), AES.MODE_ECB)
        encrypted_data = base64.urlsafe_b64decode(encrypted_email)
        return unpad(cipher.decrypt(encrypted_data), AES.block_size).decode('utf-8')
    except Exception as e:
        return None


# Mask email
def mask_email(email):
    name, domain = email.split('@')
    return name[0] + "***" + name[-1] + "@" + domain


@app.route("/verify_prescription_access", methods=["GET"])
def verify_prescription_access():
    token = request.args.get("token")
    if not token:
        return jsonify({"status": False, "message": "Missing token."}), 400

    try:
        decoded_token = base64.urlsafe_b64decode(token.encode()).decode()
        consultation_id, encrypted_email = decoded_token.split(":")
        email = decrypt_email(encrypted_email)
        if not email:
            return jsonify({"status": False, "message": "Invalid token."}), 400

        # Check consultation exists
        consultation = mongo.db.consultations.find_one({"_id": consultation_id})
        if not consultation:
            return jsonify({"status": False, "message": "Consultation not found."}), 404

        # Generate 6-character secure OTP and encrypt it
        otp = secrets.token_hex(3)  # 6-character OTP
        cipher = AES.new(hashlib.sha256(SECRET_KEY.encode()).digest(), AES.MODE_ECB)
        encrypted_otp = base64.b64encode(cipher.encrypt(pad(otp.encode(), AES.block_size))).decode()

        # Store OTP record
        mongo.db.prescription_otp_verifications.insert_one({
            "email": email,
            "otp": encrypted_otp,
            "consultation_id": consultation_id,
            "created_at": datetime.datetime.utcnow(),
            "expires_at": datetime.datetime.utcnow() + datetime.timedelta(minutes=10),
            "verified": False
        })

        # Send email
        send_otp_email(email, consultation.get("patient_name", email), otp, context_word="prescription verification")
        return jsonify({
            "status": True,
            "message": f"OTP sent at {mask_email(email)}"
        }), 200

    except Exception as e:
        return jsonify({"status": False, "message": "Invalid or malformed token."}), 400


def aes_encrypt(plain_text):
    cipher = AES.new(hashlib.sha256(SECRET_KEY.encode()).digest(), AES.MODE_ECB)
    return base64.urlsafe_b64encode(cipher.encrypt(pad(plain_text.encode(), AES.block_size))).decode()


def aes_decrypt(encoded_text):
    try:
        cipher = AES.new(hashlib.sha256(SECRET_KEY.encode()).digest(), AES.MODE_ECB)
        decrypted = unpad(cipher.decrypt(base64.urlsafe_b64decode(encoded_text)), AES.block_size)
        return decrypted.decode()
    except:
        return None


# Decrypt encrypted OTP
def decrypt_otp(encrypted_otp):
    try:
        cipher = AES.new(hashlib.sha256(SECRET_KEY.encode()).digest(), AES.MODE_ECB)
        decoded = base64.b64decode(encrypted_otp)
        return unpad(cipher.decrypt(decoded), AES.block_size).decode('utf-8')
    except:
        return None


@app.route("/api/verify_prescription_otp", methods=["POST"])
def verify_prescription_otp():
    """
    Verify OTP and Retrieve Prescription
    ---
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - otp
            - token
          properties:
            otp:
              type: string
              example: "123456"
              description: One-time password sent to the user's email
            token:
              type: string
              example: "c29tZS1lbmNyeXB0ZWQtdG9rZW4="
              description: Base64 encoded token containing consultation ID and encrypted email
    responses:
      200:
        description: OTP verified and prescription retrieved successfully
        schema:
          type: object
          properties:
            status:
              type: boolean
              example: true
            message:
              type: string
              example: "OTP verified. Prescription retrieved successfully."
            prescription_data:
              type: object
      400:
        description: Missing or invalid input, or OTP verification failed
        schema:
          type: object
          properties:
            status:
              type: boolean
              example: false
            message:
              type: string
              example: "Missing otp or token"
      404:
        description: Prescription not found
        schema:
          type: object
          properties:
            status:
              type: boolean
              example: false
            message:
              type: string
              example: "Prescription not found."
    """
    data = request.json
    otp = data.get("otp")
    token = data.get("token")
    if not token:
        return jsonify({"status": False, "message": "Missing token"}), 400
    if not otp:
        return jsonify({"status": False, "message": "Missing otp"}), 400

    decoded_token = base64.urlsafe_b64decode(token.encode()).decode()
    consultation_id, encrypted_email = decoded_token.split(":")
    email = decrypt_email(encrypted_email)
    if not email or not consultation_id:
        return jsonify({"status": False, "message": "Invalid token"}), 400

    otp_record = mongo.db.prescription_otp_verifications.find_one({
        "email": email,
        "consultation_id": consultation_id,
        "verified": False,
        "expires_at": {"$gte": datetime.datetime.utcnow()}
    })

    if not otp_record:
        return jsonify({"status": False, "message": "Invalid or expired OTP."}), 400

    decrypted_stored_otp = decrypt_otp(otp_record["otp"])
    print(decrypted_stored_otp)
    if not decrypted_stored_otp or decrypted_stored_otp != otp:
        return jsonify({"status": False, "message": "Incorrect OTP."}), 400

    mongo.db.prescription_otp_verifications.update_one(
        {"_id": otp_record["_id"]},
        {"$set": {"verified": True}}
    )

    # Fetch prescription
    consultation = mongo.db.consultations.find_one({"_id": consultation_id})
    audio = mongo.db.audios.find_one({"consultation_id": consultation_id})

    if not consultation or not audio or "prescription_details" not in audio:
        return jsonify({"status": False, "message": "Prescription not found."}), 404

    prescription_data = {
        "email": consultation.get("email", ""),
        "patient_name": consultation.get("patient_name", ""),
        "phone_number": consultation.get("phone_number", ""),
        "doctor": consultation.get("doctor", ""),
        "doctor_id": consultation.get("doctor_id", ""),
        "consultation_type": consultation.get("consultation_type", ""),
        "date": consultation.get("date", ""),
        "fromTime": consultation.get("fromTime", ""),
        "toTime": consultation.get("toTime", ""),
        "created_at": consultation.get("created_at", {}).strftime("%Y-%m-%dT%H:%M:%S"),
        "created_by": consultation.get("created_by", ""),
        "status": consultation.get("status", ""),
        "id": consultation.get("_id", ""),
        "prescription_details": {
            "medications": audio.get("prescription_details", {}).get("medications", ""),
            "diagnosis": audio.get("prescription_details", {}).get("diagnosis", ""),
            "notes": audio.get("prescription_details", {}).get("notes", ""),
            "medical_history": audio.get("prescription_details", {}).get("medical_history", "")
        }
    }

    return jsonify({
        "status": True,
        "message": "OTP verified. Prescription retrieved successfully.",
        "prescription_data": prescription_data
    }), 200


@app.route("/api/send_prescription_email", methods=["POST"])
@token_required
def send_prescription_email(current_user):
    """
    Send Secure Prescription Email
    ---
    security:
      - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - consultation_id
            - recipient_email
          properties:
            consultation_id:
              type: string
              example: "65e4f0d3e8b0f5bdf5a19d91"
              description: ID of the consultation
            recipient_email:
              type: string
              example: "<EMAIL>"
              description: Email address to which the prescription will be sent
    responses:
      200:
        description: Prescription link sent successfully
        schema:
          type: object
          properties:
            status:
              type: boolean
              example: true
            message:
              type: string
              example: "Prescription link sent successfully."
      400:
        description: Missing consultation_id or recipient_email
        schema:
          type: object
          properties:
            status:
              type: boolean
              example: false
            message:
              type: string
              example: "Missing consultation_id or recipient_email"
      403:
        description: Only doctors are allowed to send prescriptions
        schema:
          type: object
          properties:
            status:
              type: boolean
              example: false
            message:
              type: string
              example: "Only doctors are allowed to send prescriptions."
      404:
        description: Prescription not found
        schema:
          type: object
          properties:
            status:
              type: boolean
              example: false
            message:
              type: string
              example: "Prescription not found."
    """
    if current_user.get("user_type") != "doctor":
        return jsonify({"status": False, "message": "Only doctors are allowed to send prescriptions."}), 403

    data = request.json
    consultation_id = data.get("consultation_id")
    recipient_email = data.get("recipient_email")

    if not consultation_id or not recipient_email:
        return jsonify({"status": False, "message": "Missing consultation_id or recipient_email"}), 400

    consultation = mongo.db.consultations.find_one({"_id": consultation_id})
    audio = mongo.db.audios.find_one({"consultation_id": consultation_id})

    if not consultation or not audio or "prescription_details" not in audio:
        return jsonify({"status": False, "message": "Prescription not found."}), 404

    # Encrypt and encode the email
    encrypted_email = encrypt_email(recipient_email)

    # Create secure token from consultation ID and encrypted email
    secure_token = f"{consultation_id}:{encrypted_email}"
    encoded_token = base64.urlsafe_b64encode(secure_token.encode()).decode()

    # Use the fixed redirect link and append the token as a query param
    redirect_link = f"https://app-dev.evernotemd.com/verify_prescription_access?token={encoded_token}"

    # Email the link
    send_email(
        recipient_email,
        "Secure Prescription Access",
        f"You can access your prescription here (valid for 15 minutes):\n{redirect_link}"
    )

    return jsonify({"status": True, "message": "Prescription link sent successfully."}), 200


@app.route("/access_shared_prescription", methods=["GET"])
def access_shared_prescription():
    token = request.args.get("token")
    if not token:
        return jsonify({"status": False, "message": "Missing token."}), 400

    record = mongo.db.secure_prescription_links.find_one({"token": token})
    if not record:
        return jsonify({"status": False, "message": "Invalid or expired link."}), 404

    if record.get("used"):
        return jsonify({"status": False, "message": "This link has already been used."}), 403

    if record.get("expires_at") < datetime.datetime.utcnow():
        return jsonify({"status": False, "message": "This link has expired."}), 403

    decrypted_token = aes_decrypt(token)
    if not decrypted_token or decrypted_token.count(":") < 2:
        return jsonify({"status": False, "message": "Token decryption failed."}), 400

    consultation_id, recipient_email, _ = decrypted_token.split(":", 2)

    consultation = mongo.db.consultations.find_one({"_id": consultation_id})
    audio = mongo.db.audios.find_one({"consultation_id": consultation_id})

    if not consultation or not audio or "prescription_details" not in audio:
        return jsonify({"status": False, "message": "Prescription not found."}), 404

    mongo.db.secure_prescription_links.update_one(
        {"_id": record["_id"]},
        {"$set": {"used": True}}
    )

    prescription_data = {
        "email": consultation.get("email", ""),
        "patient_name": consultation.get("patient_name", ""),
        "phone_number": consultation.get("phone_number", ""),
        "doctor": consultation.get("doctor", ""),
        "doctor_id": consultation.get("doctor_id", ""),
        "consultation_type": consultation.get("consultation_type", ""),
        "date": consultation.get("date", ""),
        "fromTime": consultation.get("fromTime", ""),
        "toTime": consultation.get("toTime", ""),
        "created_at": consultation.get("created_at", {}).strftime("%Y-%m-%dT%H:%M:%S"),
        "created_by": consultation.get("created_by", ""),
        "status": consultation.get("status", ""),
        "id": consultation.get("_id", ""),
        "prescription_details": {
            "medications": audio.get("prescription_details", {}).get("medications", ""),
            "diagnosis": audio.get("prescription_details", {}).get("diagnosis", ""),
            "notes": audio.get("prescription_details", {}).get("notes", ""),
            "medical_history": audio.get("prescription_details", {}).get("medical_history", "")
        }
    }

    return jsonify({
        "status": True,
        "message": "Prescription accessed successfully.",
        "prescription_data": prescription_data
    }), 200


# --- Admin Organization Management Endpoints ---
@app.route('/api/admin/orgs', methods=['GET'])
@token_required
def admin_list_organizations(current_user):
    """
    Admin: List all organizations with status
    ---
    security:
      - Bearer: []
    responses:
      200:
        description: List of organizations
      403:
        description: Unauthorized
      500:
        description: Unable to load organizations
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    try:
        projection = {"password": 0, "otp": 0}
        orgs = list(mongo.db.organizations.find({"status": {"$ne": "deleted"}}, projection))
        for org in orgs:
            org["_id"] = str(org["_id"])
        return jsonify({"status": True, "organizations": orgs}), 200
    except Exception as e:
        return jsonify({"status": False, "message": "Unable to load organizations. Please refresh or try again.",
                        "error": str(e)}), 500


@app.route('/api/admin/orgs/details', methods=['POST'])
@token_required
def admin_get_organization_details(current_user):
    """
    Admin: Get details for a specific organization
    ---
    security:
      - Bearer: []
    parameters:
      - name: org_id
        in: body
        required: true
        schema:
          type: object
          required:
            - org_id
          properties:
            org_id:
              type: string
              example: "60c72b2f5f1b2f3b8c8c8c8c"
    responses:
      200:
        description: Organization details
      403:
        description: Unauthorized
      404:
        description: Organization not found
      500:
        description: Unable to load organization details
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    try:
        data = request.get_json()
        org_id = data.get('org_id')
        org = mongo.db.organizations.find_one({"_id": ObjectId(org_id)}, {"password": 0, "otp": 0})
        if not org:
            return jsonify({"status": False, "message": "Organization not found"}), 404
        org["_id"] = str(org["_id"])
        return jsonify({"status": True, "organization": org}), 200
    except Exception as e:
        return jsonify({"status": False, "message": "Unable to load organization details. Please try again.",
                        "error": str(e)}), 500


@app.route('/api/admin/orgs/hold', methods=['POST'])
@token_required
def admin_hold_organization(current_user):
    """
    Admin: Place an organization on hold
    ---
    security:
      - Bearer: []
    parameters:
      - name: org_id
        in: body
        required: true
        schema:
          type: object
          required:
            - org_id
          properties:
            org_id:
              type: string
              example: "60c72b2f5f1b2f3b8c8c8c8c"
    responses:
      200:
        description: Organization placed on hold
      403:
        description: Unauthorized
      404:
        description: Organization not found
      500:
        description: Action failed
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    try:
        data = request.get_json()
        org_id = data.get('org_id')
        result = mongo.db.organizations.update_one({"_id": ObjectId(org_id)}, {"$set": {"status": "hold"}})
        if result.matched_count == 0:
            return jsonify({"status": False, "message": "Organization not found"}), 404
        return jsonify({"status": True, "message": "Organization placed on hold."}), 200
    except Exception as e:
        return jsonify({"status": False, "message": "Action failed. Please try again.", "error": str(e)}), 500


@app.route('/api/admin/orgs/activate', methods=['POST'])
@token_required
def admin_activate_organization(current_user):
    """
    Admin: Reactivate an organization (remove hold)
    ---
    security:
      - Bearer: []
    parameters:
      - name: org_id
        in: body
        required: true
        schema:
          type: object
          required:
            - org_id
          properties:
            org_id:
              type: string
              example: "60c72b2f5f1b2f3b8c8c8c8c"
    responses:
      200:
        description: Organization activated
      403:
        description: Unauthorized
      404:
        description: Organization not found
      500:
        description: Action failed
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    try:
        data = request.get_json()
        org_id = data.get('org_id')
        result = mongo.db.organizations.update_one({"_id": ObjectId(org_id)}, {"$set": {"status": "active"}})
        if result.matched_count == 0:
            return jsonify({"status": False, "message": "Organization not found"}), 404
        return jsonify({"status": True, "message": "Organization activated."}), 200
    except Exception as e:
        return jsonify({"status": False, "message": "Action failed. Please try again.", "error": str(e)}), 500


@app.route('/api/admin/orgs/delete', methods=['POST'])
@token_required
def admin_delete_organization(current_user):
    """
    Admin: Soft-delete an organization (set status to 'deleted')
    ---
    security:
      - Bearer: []
    parameters:
      - name: org_id
        in: body
        required: true
        schema:
          type: object
          required:
            - org_id
          properties:
            org_id:
              type: string
              example: "60c72b2f5f1b2f3b8c8c8c8c"
    responses:
      200:
        description: Organization deleted
      403:
        description: Unauthorized
      404:
        description: Organization not found
      500:
        description: Action failed
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    try:
        data = request.get_json()
        org_id = data.get('org_id')
        result = mongo.db.organizations.update_one({"_id": ObjectId(org_id)}, {"$set": {"status": "deleted"}})
        if result.matched_count == 0:
            return jsonify({"status": False, "message": "Organization not found"}), 404
        return jsonify({"status": True, "message": "Organization deleted."}), 200
    except Exception as e:
        return jsonify({"status": False, "message": "Action failed. Please try again.", "error": str(e)}), 500


@app.route('/api/admin/orgs/available', methods=['GET'])
@token_required
def admin_list_available_organizations(current_user):
    """
    Admin: List organizations available for package requests (excluding those already in org_package_requests)
    ---
    security:
      - Bearer: []
    responses:
      200:
        description: List of available organizations with id and name only
        schema:
          type: object
          properties:
            status:
              type: boolean
            organizations:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                  name:
                    type: string
      403:
        description: Unauthorized
      500:
        description: Unable to load organizations
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403

    try:
        # Get all organization emails that already have package requests
        existing_package_emails = list(mongo.db.org_package_requests.distinct("organization_email"))

        # Find organizations not in the package requests list
        query = {
            "status": {"$ne": "deleted"},
            "email": {"$nin": existing_package_emails}
        }

        projection = {"_id": 1, "name": 1}
        orgs = list(mongo.db.organizations.find(query, projection))

        # Format the response
        available_orgs = []
        for org in orgs:
            available_orgs.append({
                "id": str(org["_id"]),
                "name": org["name"]
            })

        return jsonify({"status": True, "organizations": available_orgs}), 200

    except Exception as e:
        return jsonify({
            "status": False,
            "message": "Unable to load available organizations. Please try again.",
            "error": str(e)
        }), 500


# --- Custom Organization Package Endpoints ---
@app.route('/api/admin/orgs/custom-package', methods=['POST'])
@token_required
def create_custom_package(current_user):
    """
    Admin: Create a custom package for an organization and generate a Stripe payment link
    ---
    security:
      - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - organization_id
            - package_details
            - doctors_max_limit
            - receptionists_max_limit
            - package_price_usd
          properties:
            organization_id:
              type: string
              description: The ID of the organization
            package_details:
              type: string
            doctors_max_limit:
              type: integer
            receptionists_max_limit:
              type: integer
            package_price_usd:
              type: number
        example:
          organization_id: "60c72b2f5f1b2f3b8c8c8c8c"
          package_details: "Premium plan with telemedicine support"
          doctors_max_limit: 10
          receptionists_max_limit: 5
          package_price_usd: 299.99
    responses:
      200:
        description: Stripe payment link generated and emailed
        schema:
          type: object
          properties:
            status:
              type: boolean
            payment_link:
              type: string
              description: Direct Stripe payment link URL
            package_request_id:
              type: string
              description: Unique identifier for the package request
      400:
        description: Validation error
      500:
        description: Unable to generate payment link
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    data = request.get_json()
    required = ["organization_id", "package_details", "doctors_max_limit",
                "receptionists_max_limit", "package_price_usd"]
    if not all(data.get(k) for k in required):
        return jsonify({"status": False, "message": "Missing required fields"}), 400

    # Get organization details
    try:
        organization = mongo.db.organizations.find_one({"_id": ObjectId(data["organization_id"])})
        if not organization:
            return jsonify({"status": False, "message": "Organization not found"}), 400
    except Exception:
        return jsonify({"status": False, "message": "Invalid organization ID"}), 400

    # Check for duplicate organization email in package requests
    existing_request = mongo.db.org_package_requests.find_one({
        "organization_email": organization["email"]
    })
    if existing_request:
        return jsonify({
            "status": False,
            "message": "A package request already exists for this organization"
        }), 400

    try:
        package_request_id = generate_package_request_id()
        package_info = {
            "package_request_id": package_request_id,
            "organization_id": data["organization_id"],
            "organization_name": organization["name"],
            "organization_email": organization["email"],
            "package_details": data["package_details"],
            "doctors_max_limit": data["doctors_max_limit"],
            "receptionists_max_limit": data["receptionists_max_limit"],
            "package_price_usd": data["package_price_usd"],
            "created_at": datetime.datetime.now()
        }

        # Store pending package in DB
        mongo.db.org_package_requests.insert_one({**package_info, "status": "pending"})

        # Create Stripe Payment Link
        try:
            # Create a product for this custom package
            product = stripe.Product.create(
                name=f"Custom Package - {organization['name']}",
                description=f"{data['package_details']} | Doctors: {data['doctors_max_limit']} | Receptionists: {data['receptionists_max_limit']}"
            )

            # Create a price for the product
            price = stripe.Price.create(
                product=product.id,
                unit_amount=int(data["package_price_usd"] * 100),  # Convert to cents
                currency='usd',
            )

            # Create the payment link
            payment_link = stripe.PaymentLink.create(
                line_items=[{
                    'price': price.id,
                    'quantity': 1,
                }],
                metadata={
                    'package_request_id': package_request_id,
                    'organization_id': data["organization_id"],
                    'organization_name': organization["name"],
                    'organization_email': organization["email"]
                }
            )

            # Update the package request with Stripe IDs
            mongo.db.org_package_requests.update_one(
                {"package_request_id": package_request_id},
                {"$set": {
                    "stripe_product_id": product.id,
                    "stripe_price_id": price.id,
                    "stripe_payment_link_id": payment_link.id
                }}
            )

            stripe_payment_url = payment_link.url

        except stripe.error.StripeError as stripe_error:
            return jsonify({
                "status": False,
                "message": "Failed to create Stripe payment link",
                "error": str(stripe_error)
            }), 500

        # Email payment link to org
        send_email(
            organization["email"],
            "Your Custom Organization Package Payment Link",
            f"Please complete your payment using this secure Stripe link: {stripe_payment_url}\n\n"
            f"Package Details:\n"
            f"- Organization: {organization['name']}\n"
            f"- Package: {data['package_details']}\n"
            f"- Max Doctors: {data['doctors_max_limit']}\n"
            f"- Max Receptionists: {data['receptionists_max_limit']}\n"
            f"- Price: ${data['package_price_usd']:.2f} USD"
        )

        return jsonify({
            "status": True,
            "payment_link": stripe_payment_url
        }), 200

    except Exception as e:
        return jsonify(
            {"status": False, "message": "Unable to generate payment link. Please try again.", "error": str(e)}), 500


@app.route('/api/stripe/webhook', methods=['POST'])
def stripe_webhook():
    """
    Stripe Webhook: Handle payment events for custom organization packages
    """
    payload = request.get_data()
    sig_header = request.headers.get('Stripe-Signature')

    try:
        # Verify the webhook signature
        event = stripe.Webhook.construct_event(payload, sig_header, STRIPE_WEBHOOK_SECRET)
    except ValueError:
        return jsonify({"status": False, "message": "Invalid payload"}), 400
    except stripe.error.SignatureVerificationError:
        return jsonify({"status": False, "message": "Invalid signature"}), 400

    # Handle the checkout.session.completed event
    if event['type'] == 'payment_intent.succeeded':
        session = event['data']['object']

        # Get the payment link ID from the session
        payment_link_id = session.get('payment_link')
        if not payment_link_id:
            return jsonify({"status": False, "message": "No payment link found in session"}), 400

        # Find the package request by payment link ID
        package_request = mongo.db.org_package_requests.find_one({
            "stripe_payment_link_id": payment_link_id,
            "status": "pending"
        })

        if not package_request:
            return jsonify({"status": False, "message": "Package request not found"}), 400

        try:
            # Find and update the existing organization
            org_email = package_request["organization_email"]
            existing_org = mongo.db.organizations.find_one({"email": org_email})

            if not existing_org:
                return jsonify({"status": False, "message": "Organization not found"}), 400

            # Update organization with package details
            update_data = {
                "package_details": package_request["package_details"],
                "doctors_max_limit": package_request["doctors_max_limit"],
                "receptionists_max_limit": package_request["receptionists_max_limit"],
                "package_price_usd": package_request["package_price_usd"],
                "is_verified": True
            }

            mongo.db.organizations.update_one(
                {"email": org_email},
                {"$set": update_data}
            )

            # Update package request status
            mongo.db.org_package_requests.update_one(
                {"package_request_id": package_request["package_request_id"]},
                {"$set": {
                    "status": "completed",
                    "stripe_session_id": session['id'],
                    "payment_completed_at": datetime.datetime.now()
                }}
            )

            # Email confirmation
            send_email(
                org_email,
                "Payment Successful - Organization Package Updated",
                f"Thank you for your payment! Your organization package has been updated.\n\n"
                f"Package Details:\n"
                f"- Package: {package_request['package_details']}\n"
                f"- Max Doctors: {package_request['doctors_max_limit']}\n"
                f"- Max Receptionists: {package_request['receptionists_max_limit']}\n"
                f"- Price: ${package_request['package_price_usd']:.2f} USD\n\n"
                f"Your organization is now verified and ready to use."
            )

            return jsonify({"status": True, "message": "Organization updated successfully"}), 200

        except Exception as e:
            return jsonify({"status": False, "message": "Failed to create organization", "error": str(e)}), 500

    return jsonify({"status": True, "message": "Event received"}), 200


@app.route('/api/admin/orgs/custom_packages_listing', methods=['GET'])
@token_required
def list_custom_packages(current_user):
    """
    Admin: List all custom organization packages
    ---
    security:
      - Bearer: []
    responses:
      200:
        description: List of all custom packages
        schema:
          type: object
          properties:
            status:
              type: boolean
            packages:
              type: array
              items:
                type: object
                properties:
                  package_request_id:
                    type: string
                  organization_name:
                    type: string
                  organization_email:
                    type: string
                  package_details:
                    type: string
                  doctors_max_limit:
                    type: integer
                  receptionists_max_limit:
                    type: integer
                  package_price_usd:
                    type: number
                  status:
                    type: string
                  created_at:
                    type: string
                  stripe_payment_link_id:
                    type: string
                  payment_completed_at:
                    type: string
      403:
        description: Unauthorized
      500:
        description: Unable to fetch packages
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403

    try:
        # Fetch all packages without filtering or pagination
        packages = list(mongo.db.org_package_requests.find(
            {},  # No filter - get all packages
            {"_id": 0}  # Exclude MongoDB _id field
        ).sort("created_at", -1))  # Sort by creation date, newest first

        # Format dates for better readability
        for package in packages:
            if package.get("created_at"):
                package["created_at"] = package["created_at"].isoformat() if hasattr(package["created_at"], 'isoformat') else str(package["created_at"])
            if package.get("payment_completed_at"):
                package["payment_completed_at"] = package["payment_completed_at"].isoformat() if hasattr(package["payment_completed_at"], 'isoformat') else str(package["payment_completed_at"])

        return jsonify({
            "status": True,
            "packages": packages
        }), 200

    except Exception as e:
        return jsonify({
            "status": False,
            "message": "Unable to fetch custom packages. Please try again.",
            "error": str(e)
        }), 500


@app.route('/api/admin/orgs/confirm-payment', methods=['POST'])
@token_required
def confirm_custom_package_payment(current_user):
    """
    Admin/Frontend: Confirm payment for a custom package and create the organization
    ---
    security:
      - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - payment_id
            - payment_status
            - package_request_id
          properties:
            payment_id:
              type: string
            payment_status:
              type: string
            package_request_id:
              type: string
        example:
          payment_id: "pay_**********"
          payment_status: "success"
          package_request_id: "a1b2c3d4-5678-90ab-cdef-**********ab"
    responses:
      200:
        description: Organization created and invite code sent
      400:
        description: Validation or payment error
      500:
        description: Unable to create organization
    """
    data = request.get_json()
    required = ["payment_id", "payment_status", "package_request_id"]
    if not all(data.get(k) for k in required):
        return jsonify({"status": False, "message": "Missing required fields"}), 400
    try:
        package = mongo.db.org_package_requests.find_one({"package_request_id": data["package_request_id"]})
        if not package:
            return jsonify({"status": False, "message": "Package request not found"}), 400
        if data["payment_status"].lower() != "success":
            mongo.db.org_package_requests.update_one({"package_request_id": data["package_request_id"]}, {
                "$set": {"status": "payment_failed", "payment_id": data["payment_id"]}})
            return jsonify({"status": False, "message": "Payment not successful"}), 400
        # Create organization
        org_email = package["organization_email"]
        if mongo.db.organizations.find_one({"email": org_email}):
            return jsonify({"status": False, "message": "Organization already exists"}), 400
        hashed_password = bcrypt.generate_password_hash("changeme123").decode("utf-8")
        organization_id = str(ObjectId())
        invite_code = secrets.token_urlsafe(8)
        org_data = {
            "_id": ObjectId(organization_id),
            "name": package["organization_name"],
            "email": org_email,
            "phone": "",
            "address": "",
            "password": hashed_password,
            "otp": None,
            "is_verified": False,
            "created_at": datetime.datetime.now(),
            "status": "active",
            "package_details": package["package_details"],
            "doctors_max_limit": package["doctors_max_limit"],
            "receptionists_max_limit": package["receptionists_max_limit"],
            "package_price_usd": package["package_price_usd"]
        }
        mongo.db.organizations.insert_one(org_data)
        # Store invite code
        mongo.db.invite_codes.insert_one({
            "organization_id": organization_id,
            "code": invite_code,
            "role": "admin",
            "user_email": org_email,
            "expires_at": datetime.datetime.now() + datetime.timedelta(days=30),
            "max_uses": 1,
            "used_count": 0,
            "is_active": True,
            "created_at": datetime.datetime.now()
        })
        # Update package request status
        mongo.db.org_package_requests.update_one({"package_request_id": data["package_request_id"]},
                                                 {"$set": {"status": "completed", "payment_id": data["payment_id"]}})
        # Email invite code
        send_email(
            org_email,
            "Your Organization Registration Invite Code",
            f"Your invite code is: {invite_code}"
        )
        return jsonify({"status": True, "message": "Organization created and invite code sent."}), 200
    except Exception as e:
        return jsonify(
            {"status": False, "message": "Unable to create organization. Please try again.", "error": str(e)}), 500


# --- Admin Patient Management Endpoints ---
@app.route('/api/admin/patients', methods=['GET'])
@token_required
def admin_list_patients(current_user):
    """
    Admin: List all patients
    ---
    security:
      - Bearer: []
    responses:
      200:
        description: List of patients
        content:
          application/json:
            schema:
              type: object
              properties:
                status: { type: boolean }
                patients:
                  type: array
                  items:
                    type: object
                    properties:
                      id: { type: string }
                      name: { type: string }
                      email: { type: string }
                      status: { type: string }
            examples:
              example-1:
                value:
                  status: true
                  patients:
                    - id: "60c72b2f5f1b2f3b8c8c8c8c"
                      name: "John Doe"
                      email: "<EMAIL>"
                      status: "Active"
                    - id: "60c72b2f5f1b2f3b8c8c8c8d"
                      name: "Jane Smith"
                      email: "<EMAIL>"
                      status: "Suspended"
      403:
        description: Unauthorized
      500:
        description: Unable to load patients
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    try:
        patients = list(mongo.db.users.find({"user_type": "patient"}, {"password": 0, "otp": 0}))
        result = []
        for p in patients:
            result.append({
                "id": str(p["_id"]),
                "name": p.get("name", ""),
                "email": p.get("email", ""),
                "status": p.get("status", "Active")
            })
        return jsonify({"status": True, "patients": result}), 200
    except Exception as e:
        return jsonify({"status": False, "message": "Unable to load patients.", "error": str(e)}), 500


@app.route('/api/admin/patients/suspend', methods=['POST'])
@token_required
def admin_suspend_patient(current_user):
    """
    Admin: Suspend a patient account
    ---
    security:
      - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - patient_id
          properties:
            patient_id:
              type: string
        example:
          patient_id: "60c72b2f5f1b2f3b8c8c8c8c"
    responses:
      200:
        description: Patient suspended
        content:
          application/json:
            schema:
              type: object
              properties:
                status: { type: boolean }
                message: { type: string }
            examples:
              example-1:
                value:
                  status: true
                  message: "Patient suspended."
      400:
        description: Patient not found
      403:
        description: Unauthorized
      500:
        description: Unable to suspend patient
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    data = request.get_json()
    patient_id = data.get('patient_id')
    try:
        result = mongo.db.users.update_one({"_id": ObjectId(patient_id), "user_type": "patient"},
                                           {"$set": {"status": "Suspended"}})
        if result.matched_count == 0:
            return jsonify({"status": False, "message": "Patient not found"}), 400
        return jsonify({"status": True, "message": "Patient suspended."}), 200
    except Exception as e:
        return jsonify(
            {"status": False, "message": "Unable to suspend patient. Please try again.", "error": str(e)}), 500


@app.route('/api/admin/patients/reactivate', methods=['POST'])
@token_required
def admin_reactivate_patient(current_user):
    """
    Admin: Reactivate a suspended patient account
    ---
    security:
      - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - patient_id
          properties:
            patient_id:
              type: string
        example:
          patient_id: "60c72b2f5f1b2f3b8c8c8c8c"
    responses:
      200:
        description: Patient reactivated
        content:
          application/json:
            schema:
              type: object
              properties:
                status: { type: boolean }
                message: { type: string }
            examples:
              example-1:
                value:
                  status: true
                  message: "Patient reactivated successfully."
      400:
        description: Patient not found or not suspended
      403:
        description: Unauthorized
      500:
        description: Unable to reactivate patient
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    data = request.get_json()
    patient_id = data.get('patient_id')
    try:
        # Check if patient exists and is suspended
        patient = mongo.db.users.find_one({"_id": ObjectId(patient_id), "user_type": "patient"})
        if not patient:
            return jsonify({"status": False, "message": "Patient not found"}), 400

        if patient.get("status") != "Suspended":
            return jsonify({"status": False, "message": "Patient is not suspended"}), 400

        # Reactivate the patient by setting status to "Active"
        result = mongo.db.users.update_one(
            {"_id": ObjectId(patient_id), "user_type": "patient"},
            {"$set": {"status": "Active"}}
        )

        if result.matched_count == 0:
            return jsonify({"status": False, "message": "Patient not found"}), 400

        return jsonify({"status": True, "message": "Patient reactivated successfully."}), 200
    except Exception as e:
        return jsonify(
            {"status": False, "message": "Unable to reactivate patient. Please try again.", "error": str(e)}), 500


@app.route('/api/admin/patients/profile', methods=['POST'])
@token_required
def admin_view_patient_profile(current_user):
    """
    Admin: View patient profile details
    ---
    security:
      - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - patient_id
          properties:
            patient_id:
              type: string
        example:
          patient_id: "60c72b2f5f1b2f3b8c8c8c8c"
    responses:
      200:
        description: Patient profile details
        content:
          application/json:
            schema:
              type: object
              properties:
                status: { type: boolean }
                profile:
                  type: object
                  properties:
                    personal_info: { type: object }
                    medical_history: { type: object }
                    consultations: { type: array, items: { type: object } }
            examples:
              example-1:
                value:
                  status: true
                  profile:
                    personal_info:
                      name: "John Doe"
                      email: "<EMAIL>"
                      phone_number: "+**********"
                      status: "Active"
                      created_at: "2024-06-10T12:00:00"
                    medical_history:
                      allergies: ["Penicillin"]
                      chronic_conditions: ["Diabetes"]
                    consultations:
                      - date: "2024-05-01"
                        doctor: "Dr. Smith"
                        reason: "Checkup"
                      - date: "2024-06-01"
                        doctor: "Dr. Adams"
                        reason: "Follow-up"
      400:
        description: Patient not found
      403:
        description: Unauthorized
      500:
        description: Unable to load profile
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    data = request.get_json()
    patient_id = data.get('patient_id')
    try:
        patient = mongo.db.users.find_one({"_id": ObjectId(patient_id), "user_type": "patient"},
                                          {"password": 0, "otp": 0})
        if not patient:
            return jsonify({"status": False, "message": "Patient not found"}), 400
        consultations = list(mongo.db.consultations.find({"email": patient.get("email")}, {"_id": 0}))
        profile = {
            "personal_info": {
                "name": patient.get("name", ""),
                "email": patient.get("email", ""),
                "phone_number": patient.get("phone_number", ""),
                "status": patient.get("status", "Active"),
                "created_at": patient.get("created_at")
            },
            "medical_history": patient.get("medical_history", {}),
            "consultations": consultations
        }
        return jsonify({"status": True, "profile": profile}), 200
    except Exception as e:
        return jsonify({"status": False, "message": "Unable to load profile.", "error": str(e)}), 500


# --- Admin Doctor Management Endpoints ---
import csv

@app.route('/api/admin/orgs/details_with_doctors', methods=['POST'])
@token_required
def admin_org_details_with_doctors(current_user):
    """
    Admin: Get organization details with associated doctors
    ---
    security:
      - Bearer: []
    parameters:
      - name: org_id
        in: body
        required: true
        schema:
          type: object
          required:
            - org_id
          properties:
            org_id:
              type: string
    responses:
      200:
        description: Organization details with doctor list
      403:
        description: Unauthorized
      404:
        description: Organization not found
      500:
        description: Unable to load details
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    try:
        data = request.get_json()
        org_id = data.get('org_id')
        org = mongo.db.organizations.find_one({"_id": ObjectId(org_id)}, {"password": 0, "otp": 0})
        if not org:
            return jsonify({"status": False, "message": "Organization not found"}), 404
        org["_id"] = str(org["_id"])
        # Get doctors for this org
        doctors = list(mongo.db.users.find({"user_type": "doctor", "organization_id": org_id}))
        doctor_list = []
        for doc in doctors:
            doctor_list.append({
                "id": str(doc["_id"]),
                "name": doc.get("name", ""),
                "email": doc.get("email", ""),
                "specialization": doc.get("specialization", ""),
                "status": doc.get("status", "Active")
            })
        org["doctors"] = doctor_list
        return jsonify({"status": True, "organization": org}), 200
    except Exception as e:
        return jsonify({"status": False, "message": "Unable to load organization details. Please try again.", "error": str(e)}), 500

@app.route('/api/admin/doctors/suspend', methods=['POST'])
@token_required
def admin_suspend_doctor(current_user):
    """
    Admin: Suspend a doctor
    ---
    security:
      - Bearer: []
    parameters:
      - name: doctor_id
        in: body
        required: true
        schema:
          type: object
          required:
            - doctor_id
          properties:
            doctor_id:
              type: string
    responses:
      200:
        description: Doctor suspended
      400:
        description: Doctor not found
      403:
        description: Unauthorized
      500:
        description: Unable to suspend doctor
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    data = request.get_json()
    doctor_id = data.get('doctor_id')
    try:
        result = mongo.db.users.update_one({"_id": ObjectId(doctor_id), "user_type": "doctor"}, {"$set": {"status": "Suspended"}})
        if result.matched_count == 0:
            return jsonify({"status": False, "message": "Unable to suspend doctor. Please try again."}), 400
        return jsonify({"status": True, "message": "Doctor suspended."}), 200
    except Exception as e:
        return jsonify({"status": False, "message": "Unable to suspend doctor. Please try again.", "error": str(e)}), 500


@app.route('/api/admin/doctors/reactivate', methods=['POST'])
@token_required
def admin_reactivate_doctor(current_user):
    """
    Admin: Reactivate a suspended doctor account
    ---
    security:
      - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - doctor_id
          properties:
            doctor_id:
              type: string
        example:
          doctor_id: "60c72b2f5f1b2f3b8c8c8c8c"
    responses:
      200:
        description: Doctor reactivated
        content:
          application/json:
            schema:
              type: object
              properties:
                status: { type: boolean }
                message: { type: string }
            examples:
              example-1:
                value:
                  status: true
                  message: "Doctor reactivated successfully."
      400:
        description: Doctor not found or not suspended
      403:
        description: Unauthorized
      500:
        description: Unable to reactivate doctor
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    data = request.get_json()
    doctor_id = data.get('doctor_id')
    try:
        # Check if doctor exists and is suspended
        doctor = mongo.db.users.find_one({"_id": ObjectId(doctor_id), "user_type": "doctor"})
        if not doctor:
            return jsonify({"status": False, "message": "Doctor not found"}), 400

        if doctor.get("status") != "Suspended":
            return jsonify({"status": False, "message": "Doctor is not suspended"}), 400

        # Reactivate the doctor by setting status to "Active" (since doctors need approval)
        result = mongo.db.users.update_one(
            {"_id": ObjectId(doctor_id), "user_type": "doctor"},
            {"$set": {"status": "Active"}}
        )

        if result.matched_count == 0:
            return jsonify({"status": False, "message": "Doctor not found"}), 400

        return jsonify({"status": True, "message": "Doctor reactivated successfully."}), 200
    except Exception as e:
        return jsonify(
            {"status": False, "message": "Unable to reactivate doctor. Please try again.", "error": str(e)}), 500


@app.route('/api/admin/doctors/change_org', methods=['POST'])
@token_required
def admin_change_doctor_org(current_user):
    """
    Admin: Change a doctor's organization
    ---
    security:
      - Bearer: []
    parameters:
      - name: doctor_id
        in: body
        required: true
        schema:
          type: object
          required:
            - doctor_id
            - new_org_id
          properties:
            doctor_id:
              type: string
            new_org_id:
              type: string
    responses:
      200:
        description: Doctor organization changed
      400:
        description: Doctor or organization not found
      403:
        description: Unauthorized
      500:
        description: Unable to change organization
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    data = request.get_json()
    doctor_id = data.get('doctor_id')
    new_org_id = data.get('new_org_id')
    try:
        org = mongo.db.organizations.find_one({"_id": ObjectId(new_org_id)})
        if not org:
            return jsonify({"status": False, "message": "Organization not found"}), 400
        result = mongo.db.users.update_one({"_id": ObjectId(doctor_id), "user_type": "doctor"}, {"$set": {"organization_id": new_org_id}})
        if result.matched_count == 0:
            return jsonify({"status": False, "message": "Unable to change organization. Please try again."}), 400
        return jsonify({"status": True, "message": "Doctor organization changed."}), 200
    except Exception as e:
        return jsonify({"status": False, "message": "Unable to change organization. Please try again.", "error": str(e)}), 500

@app.route('/api/admin/doctors/delete', methods=['POST'])
@token_required
def admin_delete_doctor(current_user):
    """
    Admin: Delete a doctor
    ---
    security:
      - Bearer: []
    parameters:
      - name: doctor_id
        in: body
        required: true
        schema:
          type: object
          required:
            - doctor_id
          properties:
            doctor_id:
              type: string
    responses:
      200:
        description: Doctor deleted
      400:
        description: Doctor not found
      403:
        description: Unauthorized
      500:
        description: Unable to delete doctor
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    data = request.get_json()
    doctor_id = data.get('doctor_id')
    try:
        result = mongo.db.users.delete_one({"_id": ObjectId(doctor_id), "user_type": "doctor"})
        if result.deleted_count == 0:
            return jsonify({"status": False, "message": "Unable to delete doctor. Please try again."}), 400
        return jsonify({"status": True, "message": "Doctor deleted."}), 200
    except Exception as e:
        return jsonify({"status": False, "message": "Unable to delete doctor. Please try again.", "error": str(e)}), 500

@app.route('/api/admin/doctors/approve', methods=['POST'])
@token_required
def admin_approve_doctor(current_user):
    """
    Admin: Approve a pending doctor
    ---
    security:
      - Bearer: []
    parameters:
      - name: doctor_id
        in: body
        required: true
        schema:
          type: object
          required:
            - doctor_id
          properties:
            doctor_id:
              type: string
    responses:
      200:
        description: Doctor approved
      400:
        description: Doctor not found
      403:
        description: Unauthorized
      500:
        description: Unable to approve doctor
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    data = request.get_json()
    doctor_id = data.get('doctor_id')
    try:
        result = mongo.db.users.update_one({"_id": ObjectId(doctor_id), "user_type": "doctor"}, {"$set": {"status": "Active"}})
        if result.matched_count == 0:
            return jsonify({"status": False, "message": "Unable to approve doctor. Please try again."}), 400
        return jsonify({"status": True, "message": "Doctor approved."}), 200
    except Exception as e:
        return jsonify({"status": False, "message": "Unable to approve doctor. Please try again.", "error": str(e)}), 500

@app.route('/api/admin/doctors/reject', methods=['POST'])
@token_required
def admin_reject_doctor(current_user):
    """
    Admin: Reject a pending doctor
    ---
    security:
      - Bearer: []
    parameters:
      - name: doctor_id
        in: body
        required: true
        schema:
          type: object
          required:
            - doctor_id
          properties:
            doctor_id:
              type: string
    responses:
      200:
        description: Doctor rejected
      400:
        description: Doctor not found
      403:
        description: Unauthorized
      500:
        description: Unable to reject doctor
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    data = request.get_json()
    doctor_id = data.get('doctor_id')
    try:
        result = mongo.db.users.update_one({"_id": ObjectId(doctor_id), "user_type": "doctor"}, {"$set": {"status": "Rejected"}})
        if result.matched_count == 0:
            return jsonify({"status": False, "message": "Unable to reject doctor. Please try again."}), 400
        return jsonify({"status": True, "message": "Doctor rejected."}), 200
    except Exception as e:
        return jsonify({"status": False, "message": "Unable to reject doctor. Please try again.", "error": str(e)}), 500

@app.route('/api/admin/doctors/export', methods=['POST'])
@token_required
def admin_export_doctors(current_user):
    """
    Admin: Export doctor data for an organization as CSV
    ---
    security:
      - Bearer: []
    parameters:
      - name: org_id
        in: body
        required: true
        schema:
          type: object
          required:
            - org_id
          properties:
            org_id:
              type: string
    responses:
      200:
        description: CSV file
      403:
        description: Unauthorized
      500:
        description: Unable to export doctor data
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    try:
        data = request.get_json()
        org_id = data.get('org_id')
        doctors = list(mongo.db.users.find({"user_type": "doctor", "organization_id": org_id}))
        output = io.StringIO()
        writer = csv.writer(output)
        writer.writerow(["Name", "Email", "Specialization", "Status"])
        for doc in doctors:
            writer.writerow([
                doc.get("name", ""),
                doc.get("email", ""),
                doc.get("specialization", ""),
                doc.get("status", "Active")
            ])
        response = make_response(output.getvalue())
        response.headers["Content-Disposition"] = "attachment; filename=doctors.csv"
        response.headers["Content-type"] = "text/csv"
        return response
    except Exception as e:
        return jsonify({"status": False, "message": "Unable to export doctor data. Please check your connection and try again.", "error": str(e)}), 500

@app.route('/api/admin/doctors/profile', methods=['POST'])
@token_required
def admin_doctor_profile(current_user):
    """
    Admin: View doctor profile with certificates and KYC documents
    ---
    security:
      - Bearer: []
    parameters:
      - name: doctor_id
        in: body
        required: true
        schema:
          type: object
          required:
            - doctor_id
          properties:
            doctor_id:
              type: string
    responses:
      200:
        description: Doctor profile details
      400:
        description: Doctor not found
      403:
        description: Unauthorized
      500:
        description: Unable to load profile
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    data = request.get_json()
    doctor_id = data.get('doctor_id')
    try:
        doctor = mongo.db.users.find_one({"_id": ObjectId(doctor_id), "user_type": "doctor"}, {"password": 0, "otp": 0})
        if not doctor:
            return jsonify({"status": False, "message": "Doctor not found"}), 400
        # Fetch certificates from doctor_certificates collection
        certs = list(mongo.db.doctor_certificates.find({"doctor_id": doctor_id}))
        cert_list = []
        for cert in certs:
            url = f"{request.host_url.rstrip('/')}/media/doctor_certificates/{os.path.basename(cert['file_path'])}"
            if "127.0.0" not in url:
                url = url.replace("http://", "https://")
            cert_list.append({
                "filename": cert.get("filename"),
                "url": url,
                "uploaded_at": cert.get("uploaded_at").isoformat() if cert.get("uploaded_at") else None
            })
        # Fetch latest KYC status from registration_progress (like /api/registration/kyc_status)
        kyc_progress = mongo.db.registration_progress.find_one(
            {"doctor_id": doctor_id},
            sort=[("last_saved", -1)]
        )
        kyc_status = None
        if kyc_progress:
            progress = kyc_progress.get("progress", {})
            if progress:
                latest_step = list(progress.keys())[-1]
                kyc_status = {
                    "latest_step": latest_step,
                    "step_data": progress[latest_step]
                }
        profile = {
            "personal_info": {
                "name": doctor.get("name", ""),
                "email": doctor.get("email", ""),
                "specialization": doctor.get("specialization", ""),
                "status": doctor.get("status", "Active")
            },
            "certificates": cert_list,
            "kyc_documents": kyc_status
        }
        return jsonify({"status": True, "profile": profile}), 200
    except Exception as e:
        return jsonify({"status": False, "message": "Unable to load profile.", "error": str(e)}), 500

@app.route('/api/admin/doctors/remove_certificate', methods=['POST'])
@token_required
def admin_remove_doctor_certificate(current_user):
    """
    Admin: Remove a doctor's certificate
    ---
    security:
      - Bearer: []
    parameters:
      - name: doctor_id
        in: body
        required: true
        schema:
          type: object
          required:
            - doctor_id
            - certificate_id
          properties:
            doctor_id:
              type: string
            certificate_id:
              type: string
              description: The filename of the certificate to remove
    responses:
      200:
        description: Certificate removed
      400:
        description: Doctor or certificate not found
      403:
        description: Unauthorized
      500:
        description: Unable to remove certificate
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    data = request.get_json()
    doctor_id = data.get('doctor_id')
    certificate_id = data.get('certificate_id')  # This is the filename

    if not doctor_id or not certificate_id:
        return jsonify({"status": False, "message": "doctor_id and certificate_id are required"}), 400

    try:
        # First, verify the doctor exists
        doctor = mongo.db.users.find_one({"_id": ObjectId(doctor_id), "user_type": "doctor"})
        if not doctor:
            return jsonify({"status": False, "message": "Doctor not found"}), 400

        # Find the certificate in doctor_certificates collection by filename
        certificate = mongo.db.doctor_certificates.find_one({
            "doctor_id": doctor_id,
            "filename": certificate_id
        })

        if not certificate:
            return jsonify({"status": False, "message": "Certificate not found"}), 400

        # Remove the certificate document from doctor_certificates collection
        result = mongo.db.doctor_certificates.delete_one({
            "doctor_id": doctor_id,
            "filename": certificate_id
        })

        if result.deleted_count == 0:
            return jsonify({"status": False, "message": "Unable to remove certificate. Please try again."}), 400

        # Remove the physical file from disk
        file_path = certificate.get('file_path')
        if file_path and os.path.isfile(file_path):
            os.remove(file_path)

        return jsonify({"status": True, "message": "Certificate removed successfully."}), 200
    except Exception as e:
        return jsonify({"status": False, "message": "Unable to remove certificate. Please try again.", "error": str(e)}), 500

@app.route('/api/admin/doctors/remove_kyc', methods=['POST'])
@token_required
def admin_remove_doctor_kyc(current_user):
    """
    Admin: Remove a doctor's KYC document
    ---
    security:
      - Bearer: []
    parameters:
      - name: doctor_id
        in: body
        required: true
        schema:
          type: object
          required:
            - doctor_id
            - kyc_path
          properties:
            doctor_id:
              type: string
            kyc_path:
              type: string
    responses:
      200:
        description: KYC document removed
      400:
        description: Doctor or KYC document not found
      403:
        description: Unauthorized
      500:
        description: Unable to remove KYC document
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    data = request.get_json()
    doctor_id = data.get('doctor_id')
    kyc_path = data.get('kyc_path')
    try:
        result = mongo.db.users.update_one({"_id": ObjectId(doctor_id), "user_type": "doctor"}, {"$pull": {"kyc_documents": kyc_path}})
        if result.matched_count == 0:
            return jsonify({"status": False, "message": "Unable to remove KYC document. Please try again."}), 400
        abs_path = os.path.join(os.getcwd(), kyc_path.lstrip("/"))
        if os.path.isfile(abs_path):
            os.remove(abs_path)
        return jsonify({"status": True, "message": "KYC document removed."}), 200
    except Exception as e:
        return jsonify({"status": False, "message": "Unable to remove KYC document. Please try again.", "error": str(e)}), 500

@app.route('/api/admin/doctor_analysis', methods=['POST'])
@token_required
def admin_doctor_analysis(current_user):
    """
    Admin: Get doctor performance metrics (total consultations, avg feedback)
    ---
    security:
      - Bearer: []
    requestBody:
      required: false
      content:
        application/json:
          schema:
            type: object
            properties:
              org_id:
                type: string
                example: "60f7c2e2b4d1c2a1b8e4d456"
              start_date:
                type: string
                format: date
                example: "2024-06-01"
              end_date:
                type: string
                format: date
                example: "2024-06-30"
    responses:
      200:
        description: Doctor performance metrics
      403:
        description: Unauthorized
      500:
        description: Unable to fetch analysis
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    try:
        data = request.get_json() or {}
        org_id = data.get('org_id')
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        metrics = aggregate_doctor_performance(mongo, org_id=org_id, start_date=start_date, end_date=end_date)
        return jsonify({"status": True, "data": metrics}), 200
    except Exception as e:
        return jsonify({"status": False, "message": "Unable to fetch analysis. Please try again.", "error": str(e)}), 500

@app.route('/api/admin/doctor_report/download', methods=['POST'])
@token_required
def admin_doctor_report_download(current_user):
    """
    Admin: Download doctor performance report as CSV
    ---
    security:
      - Bearer: []
    requestBody:
      required: false
      content:
        application/json:
          schema:
            type: object
            properties:
              org_id:
                type: string
                example: "60f7c2e2b4d1c2a1b8e4d456"
              start_date:
                type: string
                format: date
                example: "2024-06-01"
              end_date:
                type: string
                format: date
                example: "2024-06-30"
    responses:
      200:
        description: CSV file
      403:
        description: Unauthorized
      500:
        description: Unable to generate report
    """
    if current_user.get('user_type') != 'admin':
        return jsonify({"status": False, "message": "Unauthorized"}), 403
    try:
        data = request.get_json() or {}
        org_id = data.get('org_id')
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        metrics = aggregate_doctor_performance(mongo, org_id=org_id, start_date=start_date, end_date=end_date)
        filename, fileobj = generate_doctor_report_csv(metrics)
        response = make_response(fileobj.getvalue())
        response.headers["Content-Disposition"] = f"attachment; filename={filename}"
        response.headers["Content-type"] = "text/csv"
        return response
    except Exception as e:
        return jsonify({"status": False, "message": "Unable to generate report. Please try again.", "error": str(e)}), 500


if __name__ == "__main__":
    app.run(debug=True)
